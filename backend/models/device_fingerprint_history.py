"""
نموذج تاريخ بصمات الأجهزة
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, JSON
from sqlalchemy.ext.declarative import declarative_base
from database.base import Base
from utils.datetime_utils import get_tripoli_now
import json
from typing import Dict, Any, Optional

class DeviceFingerprintHistory(Base):
    """
    نموذج تاريخ بصمات الأجهزة
    """
    __tablename__ = "device_fingerprint_history"

    id = Column(Integer, primary_key=True, index=True)
    fingerprint_id = Column(String, index=True, nullable=False)
    event_type = Column(String, nullable=False)  # fingerprint_created, device_access, device_approved, etc.
    ip_address = Column(String)
    user_agent = Column(Text)
    event_data = Column(Text)  # JSON string for additional data
    created_at = Column(DateTime, default=get_tripoli_now)

    def get_event_data(self) -> Dict[str, Any]:
        """
        الحصول على بيانات الحدث كقاموس
        """
        if self.event_data:
            try:
                return json.loads(self.event_data)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}

    def set_event_data(self, data: Dict[str, Any]):
        """
        تعيين بيانات الحدث
        """
        if data:
            self.event_data = json.dumps(data)
        else:
            self.event_data = None

    def to_dict(self) -> Dict[str, Any]:
        """
        تحويل إلى قاموس
        """
        return {
            'id': self.id,
            'fingerprint_id': self.fingerprint_id,
            'event_type': self.event_type,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'event_data': self.get_event_data(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f"<DeviceFingerprintHistory(id={self.id}, fingerprint_id='{self.fingerprint_id}', event_type='{self.event_type}')>"
