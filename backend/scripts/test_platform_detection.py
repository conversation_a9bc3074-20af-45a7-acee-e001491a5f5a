#!/usr/bin/env python3
"""
اختبار كشف المنصة والمتصفح
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.device_tracker import DeviceTracker

def test_platform_detection():
    """
    اختبار كشف المنصة والمتصفح مع user agents مختلفة
    """
    tracker = DeviceTracker()
    
    test_cases = [
        # Android Chrome
        "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
        
        # iPhone Safari
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
        
        # Windows Chrome
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        
        # macOS Safari
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
        
        # Linux Firefox
        "Mozilla/5.0 (X11; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0"
    ]
    
    print("🧪 اختبار كشف المنصة والمتصفح:")
    print("=" * 60)
    
    for i, user_agent in enumerate(test_cases, 1):
        print(f"\n{i}. User Agent:")
        print(f"   {user_agent[:80]}...")
        
        result = tracker._extract_platform_info(user_agent)
        
        print(f"   النظام: {result['system']}")
        print(f"   المنصة: {result['platform']}")
        print(f"   المتصفح: {result['browser']}")
        print(f"   نوع الجهاز: {result['device_type']}")
        print("-" * 40)

if __name__ == "__main__":
    test_platform_detection()
