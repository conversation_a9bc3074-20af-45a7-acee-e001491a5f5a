#!/usr/bin/env python3
"""
إضافة حقل original_table إلى جدول blocked_devices
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from database.session import get_db
import logging

logger = logging.getLogger(__name__)

def add_original_table_column():
    """
    إضافة حقل original_table إلى جدول blocked_devices
    """
    try:
        db = next(get_db())
        
        # التحقق من وجود العمود
        check_column_query = text("""
            SELECT COUNT(*) 
            FROM pragma_table_info('blocked_devices') 
            WHERE name = 'original_table'
        """)
        
        result = db.execute(check_column_query).scalar()
        
        if result == 0:
            # إضافة العمود
            add_column_query = text("""
                ALTER TABLE blocked_devices 
                ADD COLUMN original_table VARCHAR
            """)
            
            db.execute(add_column_query)
            db.commit()
            
            logger.info("✅ تم إضافة حقل original_table إلى جدول blocked_devices بنجاح")
            print("✅ تم إضافة حقل original_table إلى جدول blocked_devices بنجاح")
        else:
            logger.info("ℹ️ حقل original_table موجود بالفعل في جدول blocked_devices")
            print("ℹ️ حقل original_table موجود بالفعل في جدول blocked_devices")
            
    except Exception as e:
        logger.error(f"خطأ في إضافة حقل original_table: {e}")
        print(f"❌ خطأ في إضافة حقل original_table: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    add_original_table_column()
