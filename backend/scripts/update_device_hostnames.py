#!/usr/bin/env python3
"""
تحديث أسماء الأجهزة الموجودة في قاعدة البيانات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import select
from database.session import get_db
from models.device_security import ApprovedDevice, PendingDevice, BlockedDevice
import logging

logger = logging.getLogger(__name__)

def update_device_hostnames():
    """
    تحديث أسماء الأجهزة بناءً على user_agent الصحيح
    """
    try:
        db = next(get_db())
        
        # قوائم الجداول للتحديث
        tables = [
            (ApprovedDevice, "approved_devices"),
            (PendingDevice, "pending_devices"), 
            (BlockedDevice, "blocked_devices")
        ]
        
        total_updated = 0
        
        for DeviceModel, table_name in tables:
            print(f"\n🔄 تحديث جدول {table_name}...")
            
            # جلب جميع الأجهزة
            stmt = select(DeviceModel)
            devices = db.execute(stmt).scalars().all()
            
            updated_count = 0
            
            for device in devices:
                user_agent = device.user_agent or ''
                old_hostname = device.hostname
                new_hostname = None
                
                # تحديد الاسم الجديد بناءً على user_agent (ترتيب مهم: Android قبل Linux)
                if 'Android' in user_agent:
                    new_hostname = 'هاتف Android'
                elif 'iPhone' in user_agent or 'iPad' in user_agent:
                    new_hostname = 'جهاز iOS'
                elif 'Windows' in user_agent:
                    new_hostname = 'جهاز Windows'
                elif 'Mac' in user_agent:
                    new_hostname = 'جهاز Mac'
                elif 'Linux' in user_agent:
                    new_hostname = 'جهاز Linux'
                
                # تحديث إذا كان هناك تغيير
                if new_hostname and new_hostname != old_hostname:
                    device.hostname = new_hostname
                    updated_count += 1
                    print(f"  ✅ {device.device_id}: '{old_hostname}' → '{new_hostname}'")
            
            if updated_count > 0:
                db.commit()
                print(f"  📊 تم تحديث {updated_count} جهاز في {table_name}")
            else:
                print(f"  ℹ️ لا توجد أجهزة تحتاج تحديث في {table_name}")
            
            total_updated += updated_count
        
        print(f"\n✅ تم تحديث {total_updated} جهاز إجمالي")
        
    except Exception as e:
        logger.error(f"خطأ في تحديث أسماء الأجهزة: {e}")
        print(f"❌ خطأ في تحديث أسماء الأجهزة: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    update_device_hostnames()
