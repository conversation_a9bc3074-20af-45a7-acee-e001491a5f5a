from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List
from pydantic import BaseModel
import threading
import logging
from datetime import datetime

from database.session import get_db
from models.setting import Setting
from models.user import User
from schemas.setting import SettingCreate, SettingUpdate, SettingResponse
from utils.auth import get_current_admin_user, get_optional_user

router = APIRouter(prefix="/api/settings", tags=["settings"])
logger = logging.getLogger(__name__)

def _get_real_client_ip(request: Request) -> str:
    """
    الحصول على عنوان IP الحقيقي للعميل - محسن للتمييز بين الأجهزة البعيدة والمحلية
    """
    # التحقق من headers الخاصة بـ proxy أولاً
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # أخذ أول IP في القائمة (العميل الحقيقي)
        client_ip = forwarded_for.split(",")[0].strip()
        # التحقق من أن IP ليس محلي
        if not _is_local_ip(client_ip):
            return client_ip

    real_ip = request.headers.get("X-Real-IP")
    if real_ip and not _is_local_ip(real_ip):
        return real_ip

    # فحص headers إضافية للأجهزة البعيدة
    cf_connecting_ip = request.headers.get("CF-Connecting-IP")
    if cf_connecting_ip and not _is_local_ip(cf_connecting_ip):
        return cf_connecting_ip

    x_client_ip = request.headers.get("X-Client-IP")
    if x_client_ip and not _is_local_ip(x_client_ip):
        return x_client_ip

    # الحصول على IP المباشر
    direct_ip = request.client.host if request.client else "unknown"

    # إذا كان IP مباشر وليس محلي، استخدمه
    if direct_ip != "unknown" and not _is_local_ip(direct_ip):
        return direct_ip

    # إذا كان كل شيء محلي، تحقق من Host header لتحديد نوع الوصول
    host_header = request.headers.get("host", "")
    if host_header:
        # إذا كان Host يحتوي على IP غير محلي، فهو جهاز بعيد يصل عبر IP
        host_ip = host_header.split(":")[0]  # إزالة port إذا وجد
        if not _is_local_ip(host_ip) and _is_valid_ip(host_ip):
            # هذا يعني أن العميل يصل عبر IP الخادم، لكن من جهاز بعيد
            # نحتاج لإنشاء IP فريد للعميل البعيد
            return _generate_remote_client_ip(request)

    # افتراضي: استخدام IP المباشر حتى لو كان محلي
    return direct_ip

def _is_local_ip(ip: str) -> bool:
    """
    التحقق من أن IP محلي
    """
    if not ip or ip == "unknown":
        return True

    local_patterns = [
        "127.", "localhost", "::1", "0.0.0.0",
        "192.168.", "10.", "172.16.", "172.17.", "172.18.", "172.19.",
        "172.20.", "172.21.", "172.22.", "172.23.", "172.24.", "172.25.",
        "172.26.", "172.27.", "172.28.", "172.29.", "172.30.", "172.31."
    ]

    return any(ip.startswith(pattern) for pattern in local_patterns)

def _is_valid_ip(ip: str) -> bool:
    """
    التحقق من صحة تنسيق IP
    """
    try:
        import ipaddress
        ipaddress.ip_address(ip)
        return True
    except:
        return False

def _generate_remote_client_ip(request: Request) -> str:
    """
    إنشاء IP فريد للعميل البعيد بناءً على معلومات إضافية
    """
    try:
        # استخدام معلومات من headers لإنشاء IP فريد
        user_agent = request.headers.get("user-agent", "")
        accept_language = request.headers.get("accept-language", "")
        accept_encoding = request.headers.get("accept-encoding", "")

        # إنشاء hash من المعلومات
        import hashlib
        info_string = f"{user_agent}_{accept_language}_{accept_encoding}"
        hash_obj = hashlib.md5(info_string.encode())
        hash_hex = hash_obj.hexdigest()

        # تحويل hash إلى IP في نطاق خاص
        # استخدام نطاق 172.18.x.x للأجهزة البعيدة المميزة
        ip_suffix = int(hash_hex[:4], 16) % 65536  # 0-65535
        third_octet = ip_suffix // 256
        fourth_octet = ip_suffix % 256

        remote_ip = f"172.18.{third_octet}.{fourth_octet}"
        logger.debug(f"تم إنشاء IP فريد للعميل البعيد: {remote_ip}")

        return remote_ip

    except Exception as e:
        logger.warning(f"فشل في إنشاء IP فريد للعميل البعيد: {e}")
        # IP افتراضي للأجهزة البعيدة غير المعرفة
        return "**********"

class BatchSettingUpdate(BaseModel):
    key: str
    value: str

class BatchSettingsRequest(BaseModel):
    settings: List[BatchSettingUpdate]

@router.get("/", response_model=List[SettingResponse])
async def get_settings(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get all system settings. Only accessible by admin users.
    """
    settings = db.query(Setting).all()
    return settings

@router.get("/public", response_model=dict)
async def get_public_settings(db: Session = Depends(get_db)):
    """
    Get public settings that can be accessed without authentication.
    Returns settings as a dictionary for easy access.
    """
    settings = db.query(Setting).all()
    settings_dict = {}

    for setting in settings:
        # Convert numeric values
        if setting.key in ['tax_rate']:
            try:
                settings_dict[setting.key] = float(str(setting.value))
            except (ValueError, TypeError):
                settings_dict[setting.key] = 0.0
        else:
            settings_dict[setting.key] = str(setting.value)

    return settings_dict

@router.post("/", response_model=SettingResponse, status_code=status.HTTP_201_CREATED)
async def create_setting(
    setting: SettingCreate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Create a new setting. Only accessible by admin users.
    """
    db_setting = db.query(Setting).filter(Setting.key == setting.key).first()
    if db_setting:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Setting with this key already exists"
        )

    new_setting = Setting(**setting.model_dump())
    db.add(new_setting)
    db.commit()
    db.refresh(new_setting)
    return new_setting

@router.get("/device-info")
async def get_device_info(request: Request):
    """
    الحصول على معلومات الجهاز والتحقق من أنه الخادم الرئيسي
    """
    try:
        # الحصول على عنوان IP العميل الحقيقي
        client_ip = _get_real_client_ip(request)

        # الحصول على معلومات تمييز الجهاز
        detection_info = {
            'is_main_server': client_ip in ['*************', '127.0.0.1', 'localhost'],
            'hostname': 'جهاز غير معروف'
        }

        return {
            "client_ip": client_ip,
            "is_main_server": detection_info["is_main_server"],
            "is_local_access": detection_info["is_local_access"],
            "current_device": detection_info["current_device"],
            "server_identity": detection_info["server_identity"],
            "detection_method": detection_info["detection_method"],
            "status": detection_info["status"],
            "message": "وصول محلي - الجهاز الرئيسي" if detection_info["is_main_server"] else "وصول بعيد - جهاز في الشبكة",
            "error": detection_info.get("error")
        }

    except Exception as e:
        logger.error(f"خطأ في الحصول على معلومات الجهاز: {str(e)}")
        return {
            "client_ip": "unknown",
            "is_main_server": False,  # افتراضي آمن في حالة الخطأ
            "is_local_access": False,
            "current_device": {"error": str(e)},
            "server_identity": None,
            "detection_method": "fallback",
            "status": "error",
            "message": "خطأ في تمييز الجهاز",
            "error": str(e)
        }

@router.get("/connected-devices")
async def get_connected_devices(request: Request, current_user = Depends(get_optional_user)):
    """
    الحصول على قائمة الأجهزة المتصلة - مبسط لقاعدة البيانات فقط
    """
    try:
        # استخدام خدمة تتبع الأجهزة المبسطة
        from services.device_tracker import device_tracker

        # الحصول على عنوان IP العميل الحقيقي
        client_ip = _get_real_client_ip(request)
        user_agent = request.headers.get("user-agent", "")

        # الحصول على اسم المستخدم إذا كان مسجل دخوله
        username = current_user.username if current_user else None

        # تسجيل نشاط الجهاز (مبسط)
        await device_tracker.register_device_activity(client_ip, user_agent, username, dict(request.headers))

        # جلب البيانات من قاعدة البيانات مباشرة
        devices_data = await device_tracker.get_devices_from_database()

        # إضافة معلومات العميل الحالي
        devices_data['current_client_ip'] = client_ip
        # إرجاع البيانات مباشرة
        return devices_data



    except Exception as e:
        logger.error(f"خطأ في الحصول على الأجهزة المتصلة: {str(e)}")
        return {
            "success": False,
            "current_client_ip": "unknown",
            "summary": {
                "total_devices": 0,
                "main_server_count": 0,
                "local_devices_count": 0,
                "remote_devices_count": 0,
                "online_devices": 0,
                "recently_active_devices": 0,
                "offline_devices": 0,
                "pending_approval_devices": 0,
                "blocked_devices_count": 0,
                "pending_devices_count": 0,
                "approved_devices_count": 0
            },
            "devices": [],
            "error": str(e),
            "message": "خطأ في جلب قائمة الأجهزة المتصلة"
        }

@router.post("/update-device-user")
async def update_device_user(request: Request, current_user = Depends(get_optional_user)):
    """
    تحديث معلومات المستخدم للجهاز الحالي - للاستخدام عند تسجيل الدخول/الخروج
    """
    try:
        # from utils.device_detection import device_detector  # تم إزالة هذا الملف
        from services.device_tracker import device_tracker

        # الحصول على معلومات الجهاز
        client_ip = _get_real_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        username = current_user.username if current_user else None

        # معلومات إضافية للتحديث المحسن
        update_type = request.headers.get("X-Device-Update-Type", "user_change")
        timestamp = request.headers.get("X-Device-Timestamp", "")

        # تحديث فوري للجهاز مع معلومات محسنة
        request_headers = dict(request.headers)
        request_headers['X-User-Change-Event'] = 'true'
        request_headers['X-Update-Type'] = update_type
        request_headers['X-Force-Update'] = 'true'

        # تم إزالة device_detector.register_device_access

        # ✅ تحديث خدمة تتبع الأجهزة مباشرة مع إجبار التحديث
        try:
            # تسجيل النشاط في خدمة تتبع الأجهزة مع إجبار تحديث المستخدم
            await device_tracker.register_device_activity(client_ip, user_agent, username, request_headers)

            # ✅ تحديث إضافي مباشر في قاعدة البيانات
            await device_tracker._force_update_device_user(client_ip, username)

            logger.info(f"✅ تم تحديث المستخدم في خدمة تتبع الأجهزة: {client_ip} -> {username}")
        except Exception as e:
            logger.warning(f"خطأ في تحديث خدمة تتبع الأجهزة: {e}")

        # ✅ تحديث WebSocket فوري ومؤجل للضمان
        import asyncio

        # تحديث فوري أولاً
        asyncio.create_task(device_tracker.immediate_broadcast_update())

        # تحديث مؤجل للضمان
        asyncio.create_task(
            device_tracker._delayed_broadcast_update(0.1)  # ✅ تأخير قصير جداً
        )

        return {
            "success": True,
            "message": "تم تحديث معلومات المستخدم للجهاز",
            "client_ip": client_ip,
            "current_user": username,
            "update_type": update_type,
            "timestamp": timestamp or datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"خطأ في تحديث معلومات المستخدم للجهاز: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "فشل في تحديث معلومات المستخدم للجهاز"
        }

@router.post("/cleanup-devices")
async def cleanup_devices(current_user = Depends(get_current_admin_user)):
    """
    تنظيف وإصلاح بيانات الأجهزة المتصلة
    """
    try:
        from services.device_tracker import device_tracker

        # ✅ استخدام الدالة الجديدة للتنظيف الفوري
        result = await device_tracker.cleanup_inactive_devices_once()

        if result.get('success', True):
            return {
                "success": True,
                "data": result,
                "message": "تم تنظيف الأجهزة بنجاح"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get('error', 'فشل في تنظيف الأجهزة')
            )

    except Exception as e:
        logger.error(f"خطأ في تنظيف الأجهزة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تنظيف الأجهزة: {str(e)}"
        )

@router.post("/deduplicate-devices")
async def deduplicate_devices(current_user = Depends(get_current_admin_user)):
    """
    إزالة الأجهزة المكررة بنفس IP
    """
    try:
        from utils.device_deduplication import deduplicate_devices

        result = deduplicate_devices()

        if result['success']:
            return {
                "success": True,
                "data": result,
                "message": result['message']
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get('message', 'فشل في إزالة تكرار الأجهزة')
            )

    except Exception as e:
        logger.error(f"خطأ في إزالة تكرار الأجهزة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في إزالة تكرار الأجهزة: {str(e)}"
        )

@router.get("/duplicate-report")
async def get_duplicate_report(current_user = Depends(get_current_admin_user)):
    """
    الحصول على تقرير الأجهزة المكررة
    """
    try:
        from utils.device_deduplication import get_duplicate_report

        result = get_duplicate_report()

        if result['success']:
            return {
                "success": True,
                "data": result,
                "message": "تم إنشاء تقرير التكرار بنجاح"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get('error', 'فشل في إنشاء تقرير التكرار')
            )

    except Exception as e:
        logger.error(f"خطأ في إنشاء تقرير التكرار: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في إنشاء تقرير التكرار: {str(e)}"
        )

@router.post("/full-system-cleanup")
async def full_system_cleanup(current_user = Depends(get_current_admin_user)):
    """
    تنظيف شامل للنظام - إزالة جميع التكرارات
    """
    try:
        from utils.system_cleanup import full_system_cleanup

        result = full_system_cleanup()

        if result['success']:
            return {
                "success": True,
                "data": result,
                "message": result['message']
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get('message', 'فشل في التنظيف الشامل')
            )

    except Exception as e:
        logger.error(f"خطأ في التنظيف الشامل: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في التنظيف الشامل: {str(e)}"
        )

@router.get("/system-status")
async def get_system_status(current_user = Depends(get_current_admin_user)):
    """
    الحصول على حالة النظام وفحص التكرارات
    """
    try:
        from utils.system_cleanup import get_system_status

        result = get_system_status()

        if result['success']:
            return {
                "success": True,
                "data": result,
                "message": "تم فحص حالة النظام بنجاح"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get('error', 'فشل في فحص حالة النظام')
            )

    except Exception as e:
        logger.error(f"خطأ في فحص حالة النظام: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في فحص حالة النظام: {str(e)}"
        )

@router.post("/comprehensive-cleanup")
async def comprehensive_device_cleanup(current_user = Depends(get_current_admin_user)):
    """
    تنظيف شامل للأجهزة المكررة - إزالة جميع التكرارات نهائياً
    """
    try:
        from services.device_tracker import device_tracker

        # ✅ استخدام الدالة الجديدة للتنظيف الفوري
        result = await device_tracker.cleanup_inactive_devices_once()

        if result.get('success', True):
            return {
                "success": True,
                "data": result,
                "message": "تم التنظيف الشامل بنجاح"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get('error', 'فشل في التنظيف الشامل')
            )

    except Exception as e:
        logger.error(f"خطأ في التنظيف الشامل للأجهزة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في التنظيف الشامل للأجهزة: {str(e)}"
        )

@router.post("/cleanup-inactive-devices")
async def cleanup_inactive_devices(current_user = Depends(get_current_admin_user)):
    """
    تنظيف الأجهزة غير النشطة (التي لم تصل منذ أكثر من ساعة)
    """
    try:
        # ✅ استخدام خدمة التنظيف التلقائي بدلاً من device_tracker
        from utils.auto_device_cleanup import force_cleanup

        # تنفيذ تنظيف فوري
        result = force_cleanup()

        if result['success']:
            logger.info(f"تم تنظيف الأجهزة غير النشطة: حذف {result['removed_count']} جهاز")
            return {
                "success": True,
                "message": f"تم تنظيف {result['removed_count']} جهاز غير نشط",
                "details": {
                    "original_count": result['original_count'],
                    "final_count": result['final_count'],
                    "removed_count": result['removed_count'],
                    "approved_devices_preserved": result.get('approved_devices_count', 0),
                    "main_server_preserved": result.get('main_server_count', 0)
                }
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get('error', 'فشل في تنظيف الأجهزة غير النشطة')
            )

    except Exception as e:
        logger.error(f"خطأ في تنظيف الأجهزة غير النشطة: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تنظيف الأجهزة غير النشطة: {str(e)}"
        )

@router.delete("/connected-devices/{device_id}")
async def delete_connected_device(
    device_id: str,
    current_user = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    حذف جهاز من الأجهزة المتصلة ومن قاعدة البيانات نهائياً
    """
    try:
        from services.device_security import device_security
        from services.device_tracker import device_tracker

        # التحقق من وجود الجهاز في قاعدة البيانات
        devices_data = await device_tracker.get_devices_from_database()
        device_info = None

        for device in devices_data.get('devices', []):
            if device.get('device_id') == device_id:
                device_info = device
                break

        if device_info is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="الجهاز غير موجود في قائمة الأجهزة المتصلة"
            )

        # التحقق من أن الجهاز ليس الخادم الرئيسي
        if device_info.get('is_main_server', False):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="لا يمكن حذف الخادم الرئيسي"
            )

        # حذف الجهاز من قائمة الأجهزة المتصلة (تم إزالة هذه الوظيفة)
        connected_result = {"success": True, "message": "تم حذف الجهاز"}

        # حذف الجهاز من قاعدة البيانات
        db_result = device_security.delete_device_completely(device_id, db)

        # تجميع النتائج
        result = {
            "success": True,
            "device_id": device_id,
            "device_info": {
                "hostname": device_info.get('hostname', 'غير معروف'),
                "client_ip": device_info.get('client_ip', 'غير معروف'),
                "device_type": device_info.get('device_type', 'غير معروف')
            },
            "connected_devices_result": connected_result,
            "database_result": db_result,
            "message": f"تم حذف الجهاز '{device_info.get('hostname', 'غير معروف')}' نهائياً من النظام"
        }

        logger.info(f"تم حذف الجهاز {device_id} بواسطة المستخدم {current_user.username}")

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في حذف الجهاز {device_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في حذف الجهاز: {str(e)}"
        )

@router.post("/cleanup-all-devices")
async def cleanup_all_devices(
    current_user = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    حذف جميع بيانات الأجهزة مع الحفاظ على الخادم الرئيسي
    """
    try:
        from services.device_security import DeviceSecurityService

        device_security = DeviceSecurityService()
        result = device_security.cleanup_all_devices_except_main_server(db)

        if result['success']:
            logger.info(f"تم تنظيف جميع بيانات الأجهزة بواسطة المستخدم {current_user.username}")
            return {
                "success": True,
                "data": result,
                "message": result['message']
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get('message', 'فشل في تنظيف بيانات الأجهزة')
            )

    except Exception as e:
        logger.error(f"خطأ في تنظيف جميع بيانات الأجهزة: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في تنظيف بيانات الأجهزة: {str(e)}"
        )

@router.delete("/connected-devices/{device_id}/complete")
async def delete_device_completely(
    device_id: str,
    current_user = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    حذف جهاز نهائياً من جميع أماكن التخزين (ملف JSON + قاعدة البيانات + البصمات)
    """
    try:
        # from utils.device_detection import device_detector  # تم إزالة هذا الملف
        from services.device_security import DeviceSecurityService
        from services.device_tracker import device_tracker

        device_security = DeviceSecurityService()

        # التحقق من وجود الجهاز في قاعدة البيانات
        devices_data = await device_tracker.get_devices_from_database()
        device_info = None

        for device in devices_data.get('devices', []):
            if device.get('device_id') == device_id:
                device_info = device
                break

        if device_info is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="الجهاز غير موجود"
            )

        # التحقق من أن الجهاز ليس الخادم الرئيسي
        if device_info.get('is_main_server', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="لا يمكن حذف الخادم الرئيسي"
            )

        # حذف من ملف الأجهزة المتصلة (تم إزالة هذه الوظيفة)
        connected_result = {"success": True, "message": "تم حذف الجهاز"}

        # حذف من قاعدة البيانات (جميع الجداول)
        db_result = device_security.delete_device_completely(device_id, db)

        # تجميع النتائج
        result = {
            "success": True,
            "device_id": device_id,
            "device_info": {
                "hostname": device_info.get('hostname', 'غير معروف'),
                "client_ip": device_info.get('client_ip', 'غير معروف'),
                "device_type": device_info.get('device_type', 'غير معروف')
            },
            "connected_devices_result": connected_result,
            "database_result": db_result,
            "message": f"تم حذف الجهاز '{device_info.get('hostname', 'غير معروف')}' نهائياً من جميع أماكن التخزين"
        }

        # ✅ إرسال إشعار WebSocket لتحديث جميع العملاء
        try:
            import asyncio
            async def notify_device_deleted():
                await device_tracker.broadcast_device_update()
                logger.info(f"✅ تم إرسال إشعار WebSocket بحذف الجهاز: {device_id}")

            # تشغيل الإشعار في الخلفية
            asyncio.create_task(notify_device_deleted())
        except Exception as notify_error:
            logger.warning(f"⚠️ فشل في إرسال إشعار WebSocket: {notify_error}")

        # ✅ إرسال إشعار WebSocket لتحديث جميع العملاء
        try:
            import asyncio
            async def notify_device_deleted():
                await device_tracker.broadcast_device_update()
                logger.info(f"✅ تم إرسال إشعار WebSocket بحذف الجهاز: {device_id}")

            # تشغيل الإشعار في الخلفية
            asyncio.create_task(notify_device_deleted())
        except Exception as notify_error:
            logger.warning(f"⚠️ فشل في إرسال إشعار WebSocket: {notify_error}")

        logger.info(f"تم حذف الجهاز {device_id} نهائياً بواسطة المستخدم {current_user.username}")

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"خطأ في حذف الجهاز نهائياً: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"خطأ في حذف الجهاز نهائياً: {str(e)}"
        )

@router.get("/{key}", response_model=SettingResponse)
async def get_setting(
    key: str,
    db: Session = Depends(get_db)
):
    """
    Get a specific setting by key. This endpoint is public.
    """
    setting = db.query(Setting).filter(Setting.key == key).first()
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Setting not found"
        )
    return setting

@router.put("/{key}", response_model=SettingResponse)
async def update_setting(
    key: str,
    setting: SettingUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Update a setting value. Only accessible by admin users.
    """
    db_setting = db.query(Setting).filter(Setting.key == key).first()
    if not db_setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Setting not found"
        )

    for key, value in setting.model_dump(exclude_unset=True).items():
        setattr(db_setting, key, value)

    db.commit()
    db.refresh(db_setting)
    return db_setting

@router.delete("/{key}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_setting(
    key: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Delete a setting. Only accessible by admin users.
    """
    setting = db.query(Setting).filter(Setting.key == key).first()
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Setting not found"
        )

    db.delete(setting)
    db.commit()
    return None

@router.post("/batch", response_model=List[SettingResponse])
async def batch_update_settings(
    request: BatchSettingsRequest,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """
    Batch update multiple settings. Only accessible by admin users.
    """
    updated_settings = []

    for setting_update in request.settings:
        # Check if setting exists
        db_setting = db.query(Setting).filter(Setting.key == setting_update.key).first()

        if db_setting:
            # Update existing setting
            setattr(db_setting, 'value', setting_update.value)
        else:
            # Create new setting
            db_setting = Setting(
                key=setting_update.key,
                value=setting_update.value
            )
            db.add(db_setting)

        updated_settings.append(db_setting)

    db.commit()

    # Refresh all settings
    for setting in updated_settings:
        db.refresh(setting)

    return updated_settings


def select_folder_with_system_dialog(result_container):
    """
    فتح نافذة اختيار المجلد باستخدام نوافذ النظام
    """
    try:
        import subprocess
        import sys
        import os

        folder_path = None

        # Linux - استخدام zenity أو kdialog
        if sys.platform.startswith('linux'):
            # محاولة استخدام zenity أولاً
            try:
                result = subprocess.run([
                    'zenity', '--file-selection', '--directory',
                    '--title=اختيار مجلد النسخ الاحتياطية'
                ], capture_output=True, text=True)

                if result.returncode == 0:
                    folder_path = result.stdout.strip()
            except FileNotFoundError:
                # محاولة استخدام kdialog كبديل
                try:
                    result = subprocess.run([
                        'kdialog', '--getexistingdirectory',
                        os.path.expanduser('~'),
                        '--title', 'اختيار مجلد النسخ الاحتياطية'
                    ], capture_output=True, text=True)

                    if result.returncode == 0:
                        folder_path = result.stdout.strip()
                except FileNotFoundError:
                    raise Exception("لا توجد أدوات اختيار المجلدات متاحة (zenity أو kdialog)")

        # Windows - استخدام tkinter أو PowerShell
        elif sys.platform == 'win32':
            try:
                import tkinter as tk
                from tkinter import filedialog

                root = tk.Tk()
                root.withdraw()
                root.attributes('-topmost', True)

                folder_path = filedialog.askdirectory(
                    title="اختيار مجلد النسخ الاحتياطية"
                )
                root.destroy()
            except ImportError:
                # استخدام PowerShell كبديل
                ps_script = '''
                Add-Type -AssemblyName System.Windows.Forms
                $browser = New-Object System.Windows.Forms.FolderBrowserDialog
                $browser.Description = "اختيار مجلد النسخ الاحتياطية"
                $browser.ShowNewFolderButton = $true
                if ($browser.ShowDialog() -eq "OK") {
                    Write-Output $browser.SelectedPath
                }
                '''
                result = subprocess.run([
                    'powershell', '-Command', ps_script
                ], capture_output=True, text=True)

                if result.returncode == 0:
                    folder_path = result.stdout.strip()

        # macOS - استخدام osascript
        elif sys.platform == 'darwin':
            script = '''
            tell application "Finder"
                set folderPath to choose folder with prompt "اختيار مجلد النسخ الاحتياطية"
                return POSIX path of folderPath
            end tell
            '''
            result = subprocess.run([
                'osascript', '-e', script
            ], capture_output=True, text=True)

            if result.returncode == 0:
                folder_path = result.stdout.strip()

        else:
            raise Exception(f"نظام التشغيل غير مدعوم: {sys.platform}")

        if folder_path and folder_path.strip():
            result_container['folder_path'] = folder_path.strip()
            result_container['success'] = True
        else:
            result_container['error'] = "لم يتم اختيار أي مجلد"
            result_container['success'] = False

    except subprocess.TimeoutExpired:
        result_container['error'] = "انتهت مهلة انتظار اختيار المجلد"
        result_container['success'] = False
    except Exception as e:
        result_container['error'] = str(e)
        result_container['success'] = False


@router.post("/select-folder")
async def select_backup_folder(request: Request):
    """
    فتح نافذة اختيار المجلد من النظام وإرجاع المسار الفعلي
    """
    try:
        # الحصول على عنوان IP العميل الحقيقي
        client_ip = _get_real_client_ip(request)

        # التحقق من أن الوصول من الخادم الرئيسي
        is_main_server = client_ip in ['*************', '127.0.0.1', 'localhost']

        if not is_main_server:
            return {
                "success": False,
                "error": "هذه الميزة متاحة فقط على الخادم الرئيسي للتطبيق",
                "client_ip": client_ip,
                "is_main_server": False,
                "message": "لا يمكن فتح نافذة اختيار المجلد من جهاز بعيد"
            }
        # حاوية لحفظ النتيجة من الـ thread
        result_container = {}

        # تشغيل اختيار المجلد في thread منفصل
        thread = threading.Thread(
            target=select_folder_with_system_dialog,
            args=(result_container,)
        )
        thread.start()
        thread.join(timeout=300)  # انتظار 5 دقائق كحد أقصى

        if thread.is_alive():
            # إذا لم ينته الـ thread في الوقت المحدد
            return {
                "success": False,
                "error": "انتهت مهلة انتظار اختيار المجلد (5 دقائق)"
            }

        # التحقق من النتيجة
        if not result_container.get('success', False):
            return {
                "success": False,
                "error": result_container.get('error', 'فشل في اختيار المجلد')
            }

        folder_path = result_container.get('folder_path', '')

        if not folder_path:
            return {
                "success": False,
                "error": "لم يتم اختيار أي مجلد"
            }

        # التحقق من وجود المجلد وإمكانية الكتابة فيه
        import os

        if not os.path.exists(folder_path):
            return {
                "success": False,
                "error": "المجلد المختار غير موجود"
            }

        if not os.access(folder_path, os.W_OK):
            return {
                "success": False,
                "error": "لا توجد صلاحيات كتابة في المجلد المختار"
            }

        return {
            "success": True,
            "folder_path": folder_path,
            "absolute_path": os.path.abspath(folder_path),
            "message": "تم اختيار المجلد بنجاح"
        }

    except Exception as e:
        logger.error(f"خطأ في اختيار المجلد: {str(e)}")
        return {
            "success": False,
            "error": f"خطأ في اختيار المجلد: {str(e)}"
        }