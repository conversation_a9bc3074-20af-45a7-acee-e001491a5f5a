"""
خدمة أمان الأجهزة المتصلة
"""

import logging
import json
from datetime import datetime
from typing import List, Dict, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import select

from models.device_security import BlockedDevice, DeviceSecuritySettings, PendingDevice, ApprovedDevice
from database.session import get_db

logger = logging.getLogger(__name__)

class DeviceSecurityService:
    """
    خدمة إدارة أمان الأجهزة
    """

    def __init__(self):
        self.default_settings = {
            "require_approval": "true",  # يتطلب موافقة للأجهزة الجديدة - مفعل للاختبار
            "auto_block_suspicious": "false",  # حظر تلقائي للأجهزة المشبوهة
            "max_devices_per_ip": "10",  # الحد الأقصى للأجهزة لكل IP - زيادة للسماح بالوصول المحلي
            "block_unknown_devices": "false",  # حظر الأجهزة غير المعروفة
            "notification_enabled": "true",  # تفعيل الإشعارات
        }

    def get_security_setting(self, key: str, db: Optional[Session] = None) -> str:
        """
        الحصول على إعداد أمان
        """
        if db is None:
            db = next(get_db())

        try:
            stmt = select(DeviceSecuritySettings).where(DeviceSecuritySettings.setting_key == key)
            setting = db.execute(stmt).scalar_one_or_none()

            if setting:
                return setting.setting_value
            else:
                # إرجاع القيمة الافتراضية
                return self.default_settings.get(key, "false")
        except Exception as e:
            logger.error(f"خطأ في الحصول على إعداد الأمان {key}: {e}")
            return self.default_settings.get(key, "false")

    def set_security_setting(self, key: str, value: str, db: Optional[Session] = None) -> bool:
        """
        تعيين إعداد أمان
        """
        if db is None:
            db = next(get_db())

        try:
            stmt = select(DeviceSecuritySettings).where(DeviceSecuritySettings.setting_key == key)
            setting = db.execute(stmt).scalar_one_or_none()

            if setting:
                setting.setting_value = value
                setting.updated_at = datetime.now()
            else:
                setting = DeviceSecuritySettings(
                    setting_key=key,
                    setting_value=value,
                    description=f"إعداد أمان: {key}"
                )
                db.add(setting)

            db.commit()
            return True
        except Exception as e:
            logger.error(f"خطأ في تعيين إعداد الأمان {key}: {e}")
            db.rollback()
            return False

    def is_device_blocked(self, device_id: str, client_ip: str, db: Optional[Session] = None) -> bool:
        """
        التحقق من حظر الجهاز - محسن للأداء مع دعم البصمة المتقدمة
        """
        should_close_db = False
        if db is None:
            db = next(get_db())
            should_close_db = True

        try:
            # البحث بـ device_id أولاً (أسرع)
            stmt = select(BlockedDevice).where(BlockedDevice.device_id == device_id)
            blocked_device = db.execute(stmt).scalar_one_or_none()

            if blocked_device:
                return True

            # إذا كان device_id يبدأ بـ fp_ (بصمة متقدمة)، ابحث أيضاً بالبصمة
            if device_id.startswith('fp_'):
                # البحث في جدول البصمات المحظورة
                from models.device_fingerprint import DeviceFingerprint
                from sqlalchemy import String
                stmt = select(BlockedDevice).join(DeviceFingerprint,
                    BlockedDevice.device_id == DeviceFingerprint.fingerprint_id.cast(String)
                ).where(DeviceFingerprint.fingerprint_id == device_id)
                blocked_device = db.execute(stmt).scalar_one_or_none()

                if blocked_device:
                    return True

            # إذا لم يوجد، ابحث بـ IP (احتياطي)
            stmt = select(BlockedDevice).where(BlockedDevice.client_ip == client_ip)
            blocked_device = db.execute(stmt).scalar_one_or_none()

            return blocked_device is not None

        except Exception as e:
            logger.error(f"خطأ في التحقق من حظر الجهاز: {e}")
            return False
        finally:
            if should_close_db:
                db.close()

    def block_device(self, device_data: Dict[str, Any], blocked_by: str, reason: Optional[str] = None, db: Optional[Session] = None) -> bool:
        """
        حظر جهاز - مع حذف من الجدول المصدر وحفظ معلومات الجدول الأصلي
        """
        if db is None:
            db = next(get_db())

        try:
            # التحقق من عدم وجود الجهاز محظور مسبقاً
            device_id = device_data.get('device_id', '')
            client_ip = device_data.get('client_ip', '')
            if self.is_device_blocked(device_id, client_ip, db):
                return True  # الجهاز محظور بالفعل

            # ✅ البحث عن الجهاز في الجداول المصدر وحذفه مع حفظ الجدول الأصلي
            original_table = None

            # البحث في جدول الأجهزة المعتمدة
            stmt = select(ApprovedDevice).where(ApprovedDevice.device_id == device_id)
            approved_device = db.execute(stmt).scalar_one_or_none()
            if approved_device:
                original_table = "approved_devices"
                db.delete(approved_device)
                logger.info(f"✅ تم حذف الجهاز من الأجهزة المعتمدة: {device_id}")

            # البحث في جدول الأجهزة المنتظرة
            if not original_table:
                stmt = select(PendingDevice).where(PendingDevice.device_id == device_id)
                pending_device = db.execute(stmt).scalar_one_or_none()
                if pending_device:
                    original_table = "pending_devices"
                    db.delete(pending_device)
                    logger.info(f"✅ تم حذف الجهاز من الأجهزة المنتظرة: {device_id}")

            # تحويل التواريخ من string إلى datetime
            first_access = device_data.get('first_access')
            last_access = device_data.get('last_access')

            if isinstance(first_access, str):
                try:
                    first_access = datetime.fromisoformat(first_access.replace('Z', '+00:00'))
                except:
                    first_access = datetime.now()
            elif first_access is None:
                first_access = datetime.now()

            if isinstance(last_access, str):
                try:
                    last_access = datetime.fromisoformat(last_access.replace('Z', '+00:00'))
                except:
                    last_access = datetime.now()
            elif last_access is None:
                last_access = datetime.now()

            # ✅ إنشاء جهاز محظور مع حفظ معلومات الجدول الأصلي
            blocked_device = BlockedDevice(
                device_id=device_data.get('device_id'),
                client_ip=device_data.get('client_ip'),
                hostname=device_data.get('hostname'),
                device_type=device_data.get('device_type'),
                system=device_data.get('system'),
                platform=device_data.get('platform'),
                browser=device_data.get('browser'),  # ✅ إضافة المتصفح
                user_agent=device_data.get('user_agent'),
                blocked_by=blocked_by,
                block_reason=reason or "محظور بواسطة المدير",
                original_table=original_table,  # ✅ حفظ الجدول الأصلي في حقل منفصل
                first_access=first_access,
                last_access=last_access,
                access_count=device_data.get('access_count', 0)
            )

            db.add(blocked_device)
            db.commit()

            # ✅ تسجيل عملية الحظر في سجل التاريخ
            try:
                from services.device_fingerprint_history_service import DeviceFingerprintHistoryService
                history_service = DeviceFingerprintHistoryService(db)

                history_service.log_device_blocked(
                    fingerprint_id=device_data.get('device_id') or '',
                    blocked_by=blocked_by,
                    reason=reason or "محظور بواسطة المدير",
                    ip_address=device_data.get('client_ip'),
                    additional_data={
                        'original_table': original_table,
                        'block_time': datetime.now().isoformat(),
                        'device_info': {
                            'hostname': device_data.get('hostname'),
                            'device_type': device_data.get('device_type'),
                            'system': device_data.get('system'),
                            'platform': device_data.get('platform')
                        }
                    }
                )
                logger.info(f"✅ تم تسجيل عملية الحظر في السجل: {device_data.get('device_id')}")
            except Exception as log_error:
                logger.warning(f"خطأ في تسجيل عملية الحظر: {log_error}")

            logger.info(f"✅ تم حظر الجهاز {device_data.get('device_id')} بواسطة {blocked_by} (الجدول الأصلي: {original_table or 'غير معروف'})")
            return True
        except Exception as e:
            logger.error(f"خطأ في حظر الجهاز: {e}")
            db.rollback()
            return False

    def unblock_device(self, device_id: str, db: Optional[Session] = None) -> bool:
        """
        إلغاء حظر جهاز - مع استعادة الجهاز إلى جدوله الأصلي
        """
        if db is None:
            db = next(get_db())

        try:
            stmt = select(BlockedDevice).where(BlockedDevice.device_id == device_id)
            blocked_device = db.execute(stmt).scalar_one_or_none()

            if not blocked_device:
                return False

            # ✅ استخراج الجدول الأصلي من الحقل المخصص
            original_table = blocked_device.original_table

            # ✅ استعادة الجهاز إلى جدوله الأصلي
            if original_table == "approved_devices":
                # استعادة إلى الأجهزة المعتمدة
                approved_device = ApprovedDevice(
                    device_id=blocked_device.device_id,
                    client_ip=blocked_device.client_ip,
                    hostname=blocked_device.hostname,
                    device_type=blocked_device.device_type,
                    system=blocked_device.system,
                    platform=blocked_device.platform,
                    browser=getattr(blocked_device, 'browser', None),  # ✅ إضافة المتصفح
                    user_agent=blocked_device.user_agent,
                    approved_by="system_restore",
                    approval_notes="تم استعادة الجهاز بعد إلغاء الحظر",
                    first_access=blocked_device.first_access,
                    last_access=blocked_device.last_access,
                    access_count=blocked_device.access_count
                )
                db.add(approved_device)
                logger.info(f"✅ تم استعادة الجهاز إلى الأجهزة المعتمدة: {device_id}")

            elif original_table == "pending_devices":
                # استعادة إلى الأجهزة المنتظرة
                pending_device = PendingDevice(
                    device_id=blocked_device.device_id,
                    client_ip=blocked_device.client_ip,
                    hostname=blocked_device.hostname,
                    device_type=blocked_device.device_type,
                    system=blocked_device.system,
                    platform=blocked_device.platform,
                    user_agent=blocked_device.user_agent,
                    status="pending",
                    review_notes="تم استعادة الجهاز بعد إلغاء الحظر",
                    first_access=blocked_device.first_access,
                    last_access=blocked_device.last_access,
                    access_count=blocked_device.access_count
                )
                db.add(pending_device)
                logger.info(f"✅ تم استعادة الجهاز إلى الأجهزة المنتظرة: {device_id}")
            else:
                # إذا لم يكن هناك جدول أصلي محدد، استعادة إلى pending_devices كافتراضي
                pending_device = PendingDevice(
                    device_id=blocked_device.device_id,
                    client_ip=blocked_device.client_ip,
                    hostname=blocked_device.hostname,
                    device_type=blocked_device.device_type,
                    system=blocked_device.system,
                    platform=blocked_device.platform,
                    user_agent=blocked_device.user_agent,
                    status="pending",
                    review_notes="تم استعادة الجهاز بعد إلغاء الحظر (افتراضي)",
                    first_access=blocked_device.first_access,
                    last_access=blocked_device.last_access,
                    access_count=blocked_device.access_count
                )
                db.add(pending_device)
                logger.info(f"✅ تم استعادة الجهاز إلى الأجهزة المنتظرة (افتراضي): {device_id}")

            # حذف من الأجهزة المحظورة
            db.delete(blocked_device)
            db.commit()

            logger.info(f"✅ تم إلغاء حظر الجهاز واستعادته: {device_id} (الجدول الأصلي: {original_table or 'pending_devices افتراضي'})")
            return True

        except Exception as e:
            logger.error(f"خطأ في إلغاء حظر الجهاز: {e}")
            db.rollback()
            return False

    def delete_device_completely(self, device_id: str, db: Optional[Session] = None) -> Dict[str, Any]:
        """
        حذف جهاز نهائياً من جميع جداول قاعدة البيانات وجميع أماكن التخزين
        يحذف الجهاز بالكامل من النظام وكأنه لم يتصل بالتطبيق أبداً
        """
        should_close_db = False
        if db is None:
            db = next(get_db())
            should_close_db = True

        try:
            deleted_from = []
            device_info = None

            # التحقق من أن الجهاز ليس الخادم الرئيسي
            if self._is_main_server_device(device_id):
                return {
                    "success": False,
                    "device_id": device_id,
                    "message": "لا يمكن حذف الخادم الرئيسي"
                }

            # حذف من الأجهزة المحظورة
            stmt = select(BlockedDevice).where(BlockedDevice.device_id == device_id)
            blocked_device = db.execute(stmt).scalar_one_or_none()
            if blocked_device:
                device_info = {
                    "hostname": blocked_device.hostname,
                    "client_ip": blocked_device.client_ip,
                    "device_type": blocked_device.device_type
                }
                db.delete(blocked_device)
                deleted_from.append("blocked_devices")
                logger.info(f"تم حذف الجهاز {device_id} من الأجهزة المحظورة")

            # حذف من الأجهزة المعلقة
            stmt = select(PendingDevice).where(PendingDevice.device_id == device_id)
            pending_device = db.execute(stmt).scalar_one_or_none()
            if pending_device:
                if not device_info:
                    device_info = {
                        "hostname": pending_device.hostname,
                        "client_ip": pending_device.client_ip,
                        "device_type": pending_device.device_type
                    }
                db.delete(pending_device)
                deleted_from.append("pending_devices")
                logger.info(f"تم حذف الجهاز {device_id} من الأجهزة المعلقة")

            # حذف من الأجهزة المعتمدة
            stmt = select(ApprovedDevice).where(ApprovedDevice.device_id == device_id)
            approved_device = db.execute(stmt).scalar_one_or_none()
            if approved_device:
                if not device_info:
                    device_info = {
                        "hostname": approved_device.hostname,
                        "client_ip": approved_device.client_ip,
                        "device_type": approved_device.device_type
                    }
                db.delete(approved_device)
                deleted_from.append("approved_devices")
                logger.info(f"تم حذف الجهاز {device_id} من الأجهزة المعتمدة")

            # حذف من بصمات الأجهزة
            try:
                from models.device_fingerprint import DeviceFingerprint, DeviceFingerprintHistory

                # حذف من جدول البصمات الرئيسي
                stmt = select(DeviceFingerprint).where(DeviceFingerprint.fingerprint_id == device_id)
                fingerprint = db.execute(stmt).scalar_one_or_none()
                if fingerprint:
                    db.delete(fingerprint)
                    deleted_from.append("device_fingerprints")
                    logger.info(f"تم حذف بصمة الجهاز {device_id}")

                # حذف من تاريخ البصمات
                stmt = select(DeviceFingerprintHistory).where(DeviceFingerprintHistory.fingerprint_id == device_id)
                history_records = db.execute(stmt).scalars().all()
                for record in history_records:
                    db.delete(record)
                if history_records:
                    deleted_from.append(f"device_fingerprint_history ({len(history_records)} records)")
                    logger.info(f"تم حذف {len(history_records)} سجل من تاريخ بصمات الجهاز {device_id}")

            except Exception as e:
                logger.warning(f"خطأ في حذف بصمات الجهاز {device_id}: {e}")

            db.commit()

            return {
                "success": True,
                "device_id": device_id,
                "device_info": device_info,
                "deleted_from": deleted_from,
                "message": f"تم حذف الجهاز من {len(deleted_from)} مكان في قاعدة البيانات"
            }

        except Exception as e:
            logger.error(f"خطأ في حذف الجهاز من قاعدة البيانات: {e}")
            db.rollback()
            return {
                "success": False,
                "device_id": device_id,
                "error": str(e),
                "message": "فشل في حذف الجهاز من قاعدة البيانات"
            }
        finally:
            if should_close_db:
                db.close()

    def _is_main_server_device(self, device_id: str) -> bool:
        """
        التحقق من أن الجهاز هو الخادم الرئيسي
        """
        try:
            # فحص معرفات الخادم الرئيسي المعروفة
            main_server_ids = [
                "main_server_primary",
                "main_server",
                "localhost_server",
                "primary_server"
            ]

            if device_id in main_server_ids:
                return True

            # فحص إضافي من قاعدة البيانات
            from database.session import get_db
            from models.device_security import ApprovedDevice
            from sqlalchemy import select

            try:
                db = next(get_db())
                stmt = select(ApprovedDevice).where(
                    ApprovedDevice.device_id == device_id
                )
                device = db.execute(stmt).scalar_one_or_none()

                if device and device.device_id == 'main_server_primary':
                    return True

                return False
            except Exception:
                return False

        except Exception as e:
            logger.error(f"خطأ في التحقق من الخادم الرئيسي: {e}")
            return False

    def cleanup_all_devices_except_main_server(self, db: Optional[Session] = None) -> Dict[str, Any]:
        """
        حذف جميع بيانات الأجهزة مع الحفاظ على الخادم الرئيسي
        """
        should_close_db = False
        if db is None:
            db = next(get_db())
            should_close_db = True

        try:
            cleanup_results = {
                "connected_devices_file": {"deleted": 0, "kept": 0},
                "blocked_devices": {"deleted": 0},
                "pending_devices": {"deleted": 0},
                "approved_devices": {"deleted": 0},
                "device_fingerprints": {"deleted": 0},
                "device_fingerprint_history": {"deleted": 0}
            }

            # 1. تنظيف ملف الأجهزة المتصلة - تم إزالة هذا الملف
            # لا حاجة لتنظيف ملف الأجهزة المتصلة لأنه لم يعد موجوداً
            cleanup_results["connected_devices_file"]["deleted"] = 0
            cleanup_results["connected_devices_file"]["kept"] = 0

            # 2. حذف الأجهزة المحظورة (عدا الخادم الرئيسي)
            try:
                stmt = select(BlockedDevice)
                blocked_devices = db.execute(stmt).scalars().all()

                for device in blocked_devices:
                    if not self._is_main_server_device(str(device.device_id)):
                        db.delete(device)
                        cleanup_results["blocked_devices"]["deleted"] += 1

            except Exception as e:
                logger.error(f"خطأ في حذف الأجهزة المحظورة: {e}")

            # 3. حذف الأجهزة المعلقة (عدا الخادم الرئيسي)
            try:
                stmt = select(PendingDevice)
                pending_devices = db.execute(stmt).scalars().all()

                for device in pending_devices:
                    if not self._is_main_server_device(str(device.device_id)):
                        db.delete(device)
                        cleanup_results["pending_devices"]["deleted"] += 1

            except Exception as e:
                logger.error(f"خطأ في حذف الأجهزة المعلقة: {e}")

            # 4. حذف الأجهزة المعتمدة (عدا الخادم الرئيسي)
            try:
                stmt = select(ApprovedDevice)
                approved_devices = db.execute(stmt).scalars().all()

                for device in approved_devices:
                    if not self._is_main_server_device(str(device.device_id)):
                        db.delete(device)
                        cleanup_results["approved_devices"]["deleted"] += 1

            except Exception as e:
                logger.error(f"خطأ في حذف الأجهزة المعتمدة: {e}")

            # 5. حذف بصمات الأجهزة (عدا الخادم الرئيسي)
            try:
                from models.device_fingerprint import DeviceFingerprint, DeviceFingerprintHistory

                # حذف البصمات الرئيسية
                stmt = select(DeviceFingerprint)
                fingerprints = db.execute(stmt).scalars().all()

                for fingerprint in fingerprints:
                    if not self._is_main_server_device(str(fingerprint.fingerprint_id)):
                        db.delete(fingerprint)
                        cleanup_results["device_fingerprints"]["deleted"] += 1

                # حذف تاريخ البصمات
                stmt = select(DeviceFingerprintHistory)
                history_records = db.execute(stmt).scalars().all()

                for record in history_records:
                    if not self._is_main_server_device(str(record.fingerprint_id)):
                        db.delete(record)
                        cleanup_results["device_fingerprint_history"]["deleted"] += 1

            except Exception as e:
                logger.error(f"خطأ في حذف بصمات الأجهزة: {e}")

            # تطبيق التغييرات
            db.commit()

            total_deleted = sum(
                result.get("deleted", 0)
                for result in cleanup_results.values()
                if isinstance(result, dict)
            )

            logger.info(f"تم تنظيف النظام بنجاح: حُذف {total_deleted} عنصر إجمالي")

            return {
                "success": True,
                "cleanup_results": cleanup_results,
                "total_deleted": total_deleted,
                "message": f"تم تنظيف جميع بيانات الأجهزة بنجاح (حُذف {total_deleted} عنصر مع الحفاظ على الخادم الرئيسي)"
            }

        except Exception as e:
            logger.error(f"خطأ في تنظيف بيانات الأجهزة: {e}")
            db.rollback()
            return {
                "success": False,
                "error": str(e),
                "message": "فشل في تنظيف بيانات الأجهزة"
            }
        finally:
            if should_close_db:
                db.close()

    def get_blocked_devices(self, db: Optional[Session] = None) -> List[Dict[str, Any]]:
        """
        الحصول على قائمة الأجهزة المحظورة
        """
        if db is None:
            db = next(get_db())

        try:
            stmt = select(BlockedDevice).order_by(BlockedDevice.blocked_at.desc())
            blocked_devices = db.execute(stmt).scalars().all()

            return [
                {
                    "id": device.id,
                    "device_id": device.device_id,
                    "client_ip": device.client_ip,
                    "hostname": device.hostname,
                    "device_type": device.device_type,
                    "system": device.system,
                    "platform": device.platform,
                    "blocked_by": device.blocked_by,
                    "blocked_at": device.blocked_at.isoformat() if device.blocked_at else None,
                    "block_reason": device.block_reason,
                    "is_permanent": device.is_permanent,
                    "access_count": device.access_count
                }
                for device in blocked_devices
            ]
        except Exception as e:
            logger.error(f"خطأ في الحصول على الأجهزة المحظورة: {e}")
            return []

    def requires_approval(self, db: Optional[Session] = None) -> bool:
        """
        التحقق من تطلب موافقة للأجهزة الجديدة - مع cache
        """
        # استخدام cache بسيط لتجنب الاستعلام المتكرر
        if not hasattr(self, '_approval_cache') or not hasattr(self, '_approval_cache_time'):
            self._approval_cache = None
            self._approval_cache_time = 0

        import time
        current_time = time.time()

        # إذا كان cache صالح (أقل من دقيقة)
        if self._approval_cache is not None and (current_time - self._approval_cache_time) < 60:
            return self._approval_cache

        # تحديث cache
        result = self.get_security_setting("require_approval", db) == "true"
        self._approval_cache = result
        self._approval_cache_time = current_time

        return result

    def approve_device(self, device_id: str, approved_by: str, notes: Optional[str] = None, db: Optional[Session] = None) -> bool:
        """
        اعتماد جهاز ونقله من pending إلى approved - مع دعم البصمة المتقدمة
        """
        should_close_db = False
        if db is None:
            db = next(get_db())
            should_close_db = True

        try:
            # البحث عن الجهاز في قائمة الانتظار
            stmt = select(PendingDevice).where(
                PendingDevice.device_id == device_id,
                PendingDevice.status == "pending"
            )
            pending_device = db.execute(stmt).scalar_one_or_none()

            # إذا لم يوجد بـ device_id، ابحث بالبصمة المتقدمة
            if not pending_device and device_id.startswith('fp_'):
                from models.device_fingerprint import DeviceFingerprint
                from sqlalchemy import String
                stmt = select(PendingDevice).join(DeviceFingerprint,
                    PendingDevice.device_id == DeviceFingerprint.fingerprint_id.cast(String)
                ).where(
                    DeviceFingerprint.fingerprint_id == device_id,
                    PendingDevice.status == "pending"
                )
                pending_device = db.execute(stmt).scalar_one_or_none()

            if not pending_device:
                logger.warning(f"لم يتم العثور على جهاز في الانتظار: {device_id}")
                return False

            # إنشاء جهاز معتمد جديد
            approved_device = ApprovedDevice(
                device_id=device_id,  # استخدام device_id المرسل (قد يكون بصمة متقدمة)
                client_ip=pending_device.client_ip,
                hostname=pending_device.hostname,
                device_type=pending_device.device_type,
                system=pending_device.system,
                platform=pending_device.platform,
                browser=getattr(pending_device, 'browser', None),  # ✅ إضافة المتصفح
                user_agent=pending_device.user_agent,
                current_user=pending_device.current_user,
                approved_by=approved_by,
                approval_notes=notes,
                first_access=pending_device.first_access,
                last_access=pending_device.last_access,
                access_count=pending_device.access_count
            )

            # إضافة الجهاز المعتمد
            db.add(approved_device)

            # تحديث حالة الجهاز في قائمة الانتظار
            pending_device.status = "approved"
            pending_device.reviewed_by = approved_by
            pending_device.reviewed_at = datetime.now()
            pending_device.review_notes = notes or "تم اعتماد الجهاز"

            db.commit()

            # إنشاء بيانات الاعتماد في connected_devices.json
            try:
                self._add_approved_device_to_config(approved_device, pending_device, db)
                logger.info(f"✅ تم إضافة الجهاز المعتمد إلى ملف التكوين: {device_id}")
            except Exception as config_error:
                logger.warning(f"⚠️ خطأ في إضافة الجهاز لملف التكوين: {config_error}")

            # تسجيل الموافقة في تاريخ البصمة
            try:
                from services.device_fingerprint_history_service import get_fingerprint_history_service
                history_service = get_fingerprint_history_service(db)
                history_service.log_device_approved(
                    fingerprint_id=device_id,
                    approved_by=approved_by,
                    ip_address=pending_device.client_ip,
                    additional_data={
                        'approval_notes': notes,
                        'device_hostname': pending_device.hostname,
                        'device_type': pending_device.device_type
                    }
                )
                logger.info(f"✅ تم تسجيل الموافقة في تاريخ البصمة: {device_id}")
            except Exception as history_error:
                logger.warning(f"⚠️ خطأ في تسجيل تاريخ الموافقة: {history_error}")

            # حذف الجهاز من جدول الانتظار بعد الموافقة
            try:
                db.delete(pending_device)
                db.commit()
                logger.info(f"✅ تم حذف الجهاز من جدول الانتظار بعد الموافقة: {device_id}")
            except Exception as delete_error:
                logger.warning(f"⚠️ خطأ في حذف الجهاز من جدول الانتظار: {delete_error}")
                db.rollback()



            logger.info(f"تم اعتماد الجهاز: {device_id} بواسطة {approved_by}")
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"خطأ في اعتماد الجهاز: {e}")
            return False
        finally:
            if should_close_db:
                db.close()

    def _add_approved_device_to_config(self, approved_device: ApprovedDevice, pending_device: PendingDevice, db: Session):
        """
        إضافة الجهاز المعتمد إلى ملف connected_devices.json
        """
        try:
            from utils.datetime_utils import get_tripoli_now
            import json

            # إنشاء بيانات الجهاز للملف
            device_config_data = {
                'device_id': approved_device.device_id,
                'client_ip': approved_device.client_ip,
                'hostname': approved_device.hostname or 'جهاز معتمد',
                'platform': approved_device.platform or 'غير معروف',
                'system': approved_device.system or 'غير معروف',
                'machine': 'غير معروف',
                'browser': self._extract_browser_from_user_agent(str(approved_device.user_agent or '')),
                'is_main_server': False,
                'is_local_access': False,
                'device_type': approved_device.device_type or 'جهاز بعيد',
                'first_access': approved_device.first_access.isoformat() if approved_device.first_access is not None else get_tripoli_now().isoformat(),
                'last_access': approved_device.last_access.isoformat() if approved_device.last_access is not None else get_tripoli_now().isoformat(),
                'access_count': approved_device.access_count or 1,
                'user_agent': approved_device.user_agent or '',
                'current_user': approved_device.current_user or '',
                'status': 'approved',
                'requires_approval': False,
                'approved_by': approved_device.approved_by,
                'approved_at': approved_device.approved_at.isoformat() if approved_device.approved_at is not None else get_tripoli_now().isoformat(),
                'approval_notes': approved_device.approval_notes or 'تم اعتماد الجهاز',
                # إضافة معلومات البصمة إذا كانت متوفرة
                'hardware_fingerprint': '',
                'storage_fingerprint': '',
                'screen_fingerprint': '',
                'system_fingerprint': ''
            }

            # الحصول على معلومات البصمة من جدول device_fingerprints
            try:
                from models.device_fingerprint import DeviceFingerprint
                from sqlalchemy import select

                # البحث عن البصمة في قاعدة البيانات
                stmt = select(DeviceFingerprint).where(
                    DeviceFingerprint.fingerprint_id == approved_device.device_id
                )
                fingerprint_record = db.execute(stmt).scalar_one_or_none()

                if fingerprint_record:
                    device_config_data['hardware_fingerprint'] = fingerprint_record.hardware_fingerprint or ''
                    device_config_data['storage_fingerprint'] = fingerprint_record.storage_fingerprint or ''
                    device_config_data['screen_fingerprint'] = fingerprint_record.screen_fingerprint or ''
                    device_config_data['system_fingerprint'] = fingerprint_record.system_fingerprint or ''
                    device_config_data['is_advanced_fingerprint'] = True
                    logger.info(f"✅ تم الحصول على معلومات البصمة من قاعدة البيانات: {approved_device.device_id}")
                else:
                    # محاولة الحصول من pending_device كـ fallback
                    if hasattr(pending_device, 'fingerprint_data') and pending_device.fingerprint_data is not None:
                        fingerprint_data_str = str(pending_device.fingerprint_data)
                        if fingerprint_data_str and fingerprint_data_str.strip():
                            try:
                                fingerprint_data = json.loads(fingerprint_data_str)
                                device_config_data['hardware_fingerprint'] = fingerprint_data.get('hardware_fingerprint', '')
                                device_config_data['storage_fingerprint'] = fingerprint_data.get('storage_fingerprint', '')
                                device_config_data['screen_fingerprint'] = fingerprint_data.get('screen_fingerprint', '')
                                device_config_data['system_fingerprint'] = fingerprint_data.get('system_fingerprint', '')
                                device_config_data['is_advanced_fingerprint'] = True
                                logger.info(f"✅ تم الحصول على معلومات البصمة من pending_device: {approved_device.device_id}")
                            except Exception as parse_error:
                                logger.warning(f"⚠️ خطأ في تحليل fingerprint_data: {parse_error}")
                                device_config_data['is_advanced_fingerprint'] = False
                        else:
                            logger.warning(f"⚠️ fingerprint_data فارغ للجهاز: {approved_device.device_id}")
                            device_config_data['is_advanced_fingerprint'] = False
                    else:
                        logger.warning(f"⚠️ لم يتم العثور على معلومات البصمة للجهاز: {approved_device.device_id}")
                        device_config_data['is_advanced_fingerprint'] = False

            except Exception as fingerprint_error:
                logger.error(f"❌ خطأ في الحصول على معلومات البصمة: {fingerprint_error}")
                device_config_data['is_advanced_fingerprint'] = False

            # ملاحظة: لم نعد نستخدم ملف التكوين، البيانات محفوظة في قاعدة البيانات فقط
            logger.info(f"✅ تم اعتماد الجهاز في قاعدة البيانات: {approved_device.device_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة الجهاز المعتمد: {e}")
            raise

    def _extract_browser_from_user_agent(self, user_agent: str) -> str:
        """
        استخراج اسم المتصفح من User Agent
        """
        if not user_agent:
            return 'غير معروف'

        user_agent_lower = user_agent.lower()

        if 'chrome' in user_agent_lower and 'edg' not in user_agent_lower:
            return 'Chrome'
        elif 'firefox' in user_agent_lower:
            return 'Firefox'
        elif 'safari' in user_agent_lower and 'chrome' not in user_agent_lower:
            return 'Safari'
        elif 'edg' in user_agent_lower:
            return 'Edge'
        elif 'opera' in user_agent_lower:
            return 'Opera'
        else:
            return 'غير معروف'

    def is_device_approved(self, device_id: str, db: Optional[Session] = None) -> bool:
        """
        التحقق من أن الجهاز معتمد - مع دعم البصمة المتقدمة
        """
        should_close_db = False
        if db is None:
            db = next(get_db())
            should_close_db = True

        try:
            # البحث بـ device_id أولاً
            stmt = select(ApprovedDevice).where(ApprovedDevice.device_id == device_id)
            approved_device = db.execute(stmt).scalar_one_or_none()

            if approved_device:
                return True

            # إذا كان device_id يبدأ بـ fp_ (بصمة متقدمة)، ابحث أيضاً بالبصمة
            if device_id.startswith('fp_'):
                # البحث في جدول البصمات المعتمدة
                from models.device_fingerprint import DeviceFingerprint
                from sqlalchemy import String
                stmt = select(ApprovedDevice).join(DeviceFingerprint,
                    ApprovedDevice.device_id == DeviceFingerprint.fingerprint_id.cast(String)
                ).where(DeviceFingerprint.fingerprint_id == device_id)
                approved_device = db.execute(stmt).scalar_one_or_none()

                if approved_device:
                    return True

            return False
        except Exception as e:
            logger.error(f"خطأ في فحص اعتماد الجهاز: {e}")
            return False
        finally:
            if should_close_db:
                db.close()

    def add_pending_device(self, device_data: Dict[str, Any], db: Optional[Session] = None) -> bool:
        """
        إضافة جهاز للانتظار مع منع التكرار بنفس IP
        """
        should_close_db = False
        if db is None:
            db = next(get_db())
            should_close_db = True

        try:
            device_id = device_data.get('device_id')
            client_ip = device_data.get('client_ip')

            # التحقق من عدم وجود الجهاز بنفس device_id
            stmt = select(PendingDevice).where(
                PendingDevice.device_id == device_id,
                PendingDevice.status == "pending"
            )
            existing_by_id = db.execute(stmt).scalar_one_or_none()

            # التحقق من عدم وجود أجهزة أخرى بنفس IP وحذف جميع المكررات
            stmt = select(PendingDevice).where(
                PendingDevice.client_ip == client_ip,
                PendingDevice.status == "pending"
            )
            existing_devices_by_ip = db.execute(stmt).scalars().all()

            # حذف جميع الأجهزة المكررة بنفس IP (عدا الجهاز الحالي إذا وجد)
            for existing_device in existing_devices_by_ip:
                if existing_device.device_id != device_id:
                    logger.info(f"حذف جهاز مكرر من قاعدة البيانات: {existing_device.device_id} - IP: {client_ip}")
                    db.delete(existing_device)

            if existing_devices_by_ip:
                db.flush()

            if existing_by_id:
                # تحديث الجهاز الموجود
                existing_by_id.last_access = datetime.now()
                existing_by_id.access_count = existing_by_id.access_count + 1
                existing_by_id.client_ip = client_ip  # تحديث IP في حالة تغييره
            else:
                # تحويل التواريخ من string إلى datetime
                first_access = device_data.get('first_access')
                if isinstance(first_access, str):
                    try:
                        first_access = datetime.fromisoformat(first_access.replace('Z', '+00:00'))
                    except:
                        first_access = datetime.now()
                elif first_access is None:
                    first_access = datetime.now()

                # إنشاء fingerprint_data من معلومات البصمة
                fingerprint_data = {}
                if device_data.get('hardware_fingerprint'):
                    fingerprint_data['hardware_fingerprint'] = device_data.get('hardware_fingerprint')
                if device_data.get('storage_fingerprint'):
                    fingerprint_data['storage_fingerprint'] = device_data.get('storage_fingerprint')
                if device_data.get('screen_fingerprint'):
                    fingerprint_data['screen_fingerprint'] = device_data.get('screen_fingerprint')
                if device_data.get('system_fingerprint'):
                    fingerprint_data['system_fingerprint'] = device_data.get('system_fingerprint')

                fingerprint_data_json = json.dumps(fingerprint_data) if fingerprint_data else None

                pending_device = PendingDevice(
                    device_id=device_id,
                    client_ip=client_ip,
                    hostname=device_data.get('hostname'),
                    device_type=device_data.get('device_type'),
                    system=device_data.get('system'),
                    platform=device_data.get('platform'),
                    user_agent=device_data.get('user_agent'),
                    current_user=device_data.get('current_user'),
                    first_access=first_access,
                    last_access=datetime.now(),
                    fingerprint_data=fingerprint_data_json
                )
                db.add(pending_device)

            db.commit()
            return True
        except Exception as e:
            logger.error(f"خطأ في إضافة جهاز للانتظار: {e}")
            db.rollback()
            return False
        finally:
            if should_close_db:
                db.close()

# إنشاء instance عام
device_security = DeviceSecurityService()
