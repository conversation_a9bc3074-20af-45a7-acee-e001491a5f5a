import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import {
  FaDesktop,
  FaMobile,
  FaTabletAlt,
  FaServer,
  FaFingerprint,
  FaInfoCircle,
  FaHistory,
  FaShieldAlt,
  FaNetworkWired,
  FaEye,
  FaEyeSlash,
  FaCopy,
  FaCheck,
  FaFilePdf,
  FaFileExcel,
  FaTrash
} from 'react-icons/fa';
import { Trash2, Ban } from './ui/icons';
import Modal from './Modal';
import DeleteDeviceModal from './DeleteDeviceModal';
import { ConnectedDevice } from '../hooks/useConnectedDevices';
import api from '../lib/axios';
import { formatDateTime } from '../services/dateTimeService';
import {
  exportDeviceAccessLogFromServer
} from '../services/ServerPDFExportService';

interface DeviceDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  device: ConnectedDevice | null;
  onBlock?: (device: ConnectedDevice) => void;
}

interface DeviceFingerprint {
  id: number;
  fingerprint_id: string;
  hardware_fingerprint: string;
  storage_fingerprint: string;
  screen_fingerprint?: string;
  system_fingerprint?: string;
  network_fingerprint?: string;
  fingerprint_details?: any;
  device_info?: any;
  screen_info?: any;
  system_info?: any;
  browser_info?: any;
  network_info?: any;
  additional_info?: any;
  last_ip?: string;
  last_user_agent?: string;
  is_active: boolean;
  auto_approved: boolean;
  created_at: string;
  updated_at: string;
  last_seen_at: string;
}

const DeviceDetailsModal: React.FC<DeviceDetailsModalProps> = ({ isOpen, onClose, device, onBlock }) => {
  const [activeTab, setActiveTab] = useState<'basic' | 'fingerprint' | 'history'>('basic');
  const [fingerprintData, setFingerprintData] = useState<DeviceFingerprint | null>(null);
  const [fingerprintHistory, setFingerprintHistory] = useState<any[]>([]);
  const [isLoadingFingerprint, setIsLoadingFingerprint] = useState(false);
  const [showSensitiveData, setShowSensitiveData] = useState(false);
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const [enhancedDevice, setEnhancedDevice] = useState<any>(null);

  // حالة نافذة الحذف
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // حالات pagination لسجل الوصول
  const [historyCurrentPage, setHistoryCurrentPage] = useState(1);
  const [historyPageSize] = useState(10); // عرض 10 سجلات افتراضياً

  // حالة مؤشر التحميل لتصدير PDF
  const [isExportingPDF, setIsExportingPDF] = useState(false);

  // دالة معالجة الحذف
  const handleDeleteDevice = async () => {
    if (!device) return;

    setIsDeleting(true);
    try {
      // ✅ استدعاء API الحذف مباشرة
      const response = await api.delete(`/api/settings/connected-devices/${device.device_id}/complete`);

      if (response.data.success) {
        console.log('✅ تم حذف الجهاز بنجاح:', response.data.message);

        // إرسال حدث تحديث للمكونات الأخرى
        window.dispatchEvent(new CustomEvent('devicesUpdated', {
          detail: { action: 'delete', device_id: device.device_id }
        }));

        setShowDeleteModal(false);
        onClose(); // إغلاق نافذة التفاصيل بعد الحذف
      } else {
        console.error('❌ فشل في حذف الجهاز:', response.data.message);
      }
    } catch (error) {
      console.error('❌ خطأ في حذف الجهاز:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // تحميل بيانات الجهاز الكاملة من قاعدة البيانات وملف التكوين
  useEffect(() => {
    if (isOpen && device) {
      // إعادة تعيين pagination عند فتح النافذة
      setHistoryCurrentPage(1);

      // تحميل بيانات البصمة للأجهزة البعيدة (سواء كانت متقدمة أم لا في البيانات الأولية)
      if (!device.is_main_server) {
        console.log(`🔍 تحميل بيانات الجهاز البعيد: ${device.device_id}`);
        loadFingerprintData();
      } else {
        // مسح البيانات للخادم الرئيسي
        setFingerprintData(null);
        setFingerprintHistory([]);
        setEnhancedDevice(null);
        console.log(`ℹ️ تخطي تحميل بيانات البصمة - خادم رئيسي`);
      }
    }
  }, [isOpen, device]);

  // ✅ مستمع للأحداث المخصصة للتحديث الفوري - محسن
  useEffect(() => {
    const handleDeviceUserUpdate = (event: CustomEvent) => {
      console.log('🔄 DeviceDetailsModal: تم استلام حدث تحديث المستخدم:', event.detail);

      // إعادة تحميل البيانات إذا كانت النافذة مفتوحة والجهاز متطابق
      if (isOpen && device && !device.is_main_server) {
        console.log('🔄 DeviceDetailsModal: إعادة تحميل بيانات الجهاز بعد تحديث المستخدم');
        // ✅ تحديث فوري بدون تأخير
        loadFingerprintData();
      }
    };

    // ✅ مستمع لتحديثات الأجهزة العامة من WebSocket
    const handleDevicesUpdate = (event: CustomEvent) => {
      if (isOpen && device && event.detail?.devices) {
        // البحث عن الجهاز المحدث في البيانات الجديدة
        const updatedDevice = event.detail.devices.find((d: any) => d.device_id === device.device_id);
        if (updatedDevice) {
          console.log('🔄 DeviceDetailsModal: تحديث بيانات الجهاز من WebSocket');
          // تحديث البيانات الأساسية فوراً
          setEnhancedDevice((prev: ConnectedDevice | null) => ({
            ...prev,
            ...updatedDevice
          }));
        }
      }
    };

    // إضافة مستمعي الأحداث
    window.addEventListener('deviceUserUpdated', handleDeviceUserUpdate as EventListener);
    window.addEventListener('devicesUpdated', handleDevicesUpdate as EventListener);

    return () => {
      // إزالة مستمعي الأحداث
      window.removeEventListener('deviceUserUpdated', handleDeviceUserUpdate as EventListener);
      window.removeEventListener('devicesUpdated', handleDevicesUpdate as EventListener);
    };
  }, [isOpen, device]);

  const loadFingerprintData = async () => {
    if (!device?.device_id || device.is_main_server) {
      console.log('🚫 تخطي تحميل بيانات البصمة للخادم الرئيسي');
      return;
    }

    try {
      setIsLoadingFingerprint(true);
      console.log(`🔍 تحميل بيانات البصمة للجهاز البعيد: ${device.device_id}`);

      // استدعاء الـ API الجديد للحصول على البيانات المدمجة
      const integratedResponse = await api.get(`/api/device-fingerprints/${device.device_id}/integrated-data`);

      if (integratedResponse.data?.success && integratedResponse.data.integrated_data) {
        console.log('✅ تم العثور على البيانات المدمجة للجهاز');
        console.log('📊 مصادر البيانات:', integratedResponse.data.data_sources);
        console.log('🔄 البيانات المدمجة:', integratedResponse.data.integrated_data);

        // تحديث بيانات الجهاز بالبيانات المدمجة
        const integratedData = integratedResponse.data.integrated_data;
        const enhancedDeviceData = {
          ...device,
          // البيانات الحية من ملف التكوين
          status: integratedData.status || device.status,
          current_user: integratedData.current_user || device.current_user,
          first_access: integratedData.first_access || device.first_access,
          last_access: integratedData.last_access || device.last_access,
          access_count: integratedData.access_count || device.access_count,
          session_duration_minutes: integratedData.session_duration_minutes,
          session_duration_formatted: integratedData.session_duration_formatted,
          last_activity_formatted: integratedData.last_activity_formatted,

          // البيانات الثابتة من قاعدة البيانات
          hardware_fingerprint: integratedData.hardware_fingerprint || device.hardware_fingerprint,
          storage_fingerprint: integratedData.storage_fingerprint || device.storage_fingerprint,
          screen_fingerprint: integratedData.screen_fingerprint || device.screen_fingerprint,
          system_fingerprint: integratedData.system_fingerprint || device.system_fingerprint,
          is_advanced_fingerprint: integratedData.is_advanced_fingerprint !== undefined ? integratedData.is_advanced_fingerprint : device.is_advanced_fingerprint,

          // معلومات محسنة
          security_level: integratedData.security_level,
          security_level_ar: integratedData.security_level_ar,
          access_type: integratedData.access_type,
          access_type_ar: integratedData.access_type_ar,
          status_ar: integratedData.status_ar
        };

        setEnhancedDevice(enhancedDeviceData);
        console.log('✨ تم إنشاء جهاز محسن بالبيانات المدمجة:', enhancedDeviceData);

        // تحميل بيانات البصمة إذا كانت متوفرة
        if (integratedResponse.data.static_data && integratedResponse.data.static_data.fingerprint_id) {
          setFingerprintData(integratedResponse.data.static_data);
          console.log('📋 تم تحميل بيانات البصمة من البيانات المدمجة');

          // تحميل تاريخ البصمة
          try {
            const historyResponse = await api.get(`/api/device-fingerprints/${device.device_id}/history?limit=50`);
            if (historyResponse.data?.history) {
              setFingerprintHistory(historyResponse.data.history);
              console.log(`📋 تم تحميل ${historyResponse.data.history.length} سجل من تاريخ البصمة`);
            }
          } catch (historyError) {
            console.warn('⚠️ فشل في تحميل تاريخ البصمة:', historyError);
          }
        }

        return; // نجح في الحصول على البيانات المدمجة
      }

      // إذا لم نحصل على البيانات المدمجة، نحاول الطريقة القديمة
      console.log('🔄 محاولة الحصول على التفاصيل الكاملة بالطريقة القديمة...');
      const completeResponse = await api.get(`/api/device-fingerprints/${device.device_id}/complete-details`);
      if (completeResponse.data?.success) {
        console.log('✅ تم العثور على التفاصيل الكاملة للجهاز');
        console.log('📊 مصادر البيانات:', completeResponse.data.data_sources);

        // حفظ بيانات البصمة إذا كانت متوفرة
        if (completeResponse.data.fingerprint_data) {
          setFingerprintData(completeResponse.data.fingerprint_data);
          console.log('📋 تم تحميل بيانات البصمة من قاعدة البيانات');

          // تحميل تاريخ البصمة
          try {
            const historyResponse = await api.get(`/api/device-fingerprints/${device.device_id}/history?limit=50`);
            if (historyResponse.data?.history) {
              setFingerprintHistory(historyResponse.data.history);
              console.log(`📋 تم تحميل ${historyResponse.data.history.length} سجل من تاريخ البصمة`);
            }
          } catch (historyError) {
            console.warn('⚠️ فشل في تحميل تاريخ البصمة:', historyError);
          }
        }

        // استخدام البيانات المدمجة لتحسين عرض الجهاز
        if (completeResponse.data.combined_info) {
          console.log('🔗 معلومات الجهاز المدمجة:', completeResponse.data.combined_info);

          // إنشاء جهاز محسن بالبيانات المدمجة
          const combinedInfo = completeResponse.data.combined_info;
          const enhancedDeviceData = {
            ...device,
            // تحديث البيانات من المصادر المدمجة
            system: combinedInfo.system || device.system,
            platform: combinedInfo.platform || device.platform,
            browser: combinedInfo.browser || device.browser,
            access_count: combinedInfo.access_count || device.access_count,
            is_advanced_fingerprint: combinedInfo.is_advanced_fingerprint !== undefined ? combinedInfo.is_advanced_fingerprint : device.is_advanced_fingerprint,
            hardware_fingerprint: combinedInfo.hardware_fingerprint || device.hardware_fingerprint,
            storage_fingerprint: combinedInfo.storage_fingerprint || device.storage_fingerprint,
            screen_fingerprint: combinedInfo.screen_fingerprint || device.screen_fingerprint,
            system_fingerprint: combinedInfo.system_fingerprint || device.system_fingerprint,
            network_fingerprint: combinedInfo.network_fingerprint,
            status: combinedInfo.status || device.status,
            requires_approval: combinedInfo.requires_approval !== undefined ? combinedInfo.requires_approval : device.requires_approval,
          };

          setEnhancedDevice(enhancedDeviceData);
          console.log('✨ تم إنشاء جهاز محسن بالبيانات المدمجة:', enhancedDeviceData);

          // إجبار إعادة تحديث المكون لعرض التبويبات الجديدة
          setTimeout(() => {
            console.log('🔄 إعادة تحديث التبويبات بناءً على البيانات المحسنة');
          }, 100);
        }

        return; // نجح في الحصول على البيانات الكاملة
      }

      // إذا لم نحصل على البيانات الكاملة، نحاول الطريقة التقليدية
      console.log('🔄 محاولة الحصول على البصمة بالطريقة التقليدية...');
      const response = await api.get(`/api/device-fingerprints/${device.device_id}`);
      if (response.data?.fingerprint) {
        console.log('✅ تم العثور على بيانات البصمة بالطريقة التقليدية');
        setFingerprintData(response.data.fingerprint);

        // تحميل تاريخ البصمة
        try {
          const historyResponse = await api.get(`/api/device-fingerprints/${device.device_id}/history?limit=50`);
          if (historyResponse.data?.history) {
            setFingerprintHistory(historyResponse.data.history);
            console.log(`📋 تم تحميل ${historyResponse.data.history.length} سجل من تاريخ البصمة`);
          }
        } catch (historyError) {
          console.warn('⚠️ فشل في تحميل تاريخ البصمة:', historyError);
        }
      } else {
        console.log('❌ لم يتم العثور على بيانات البصمة');
      }
    } catch (error) {
      console.warn('❌ خطأ في تحميل بيانات الجهاز:', error);
      setFingerprintData(null);
      setFingerprintHistory([]);
      setEnhancedDevice(null);
    } finally {
      setIsLoadingFingerprint(false);
    }
  };

  // دالة لتحديد نوع الجهاز الفعلي بناءً على user agent ومعلومات الجهاز
  const getActualDeviceType = () => {
    if (!device) return 'غير محدد';

    if (device.is_main_server) {
      return 'خادم رئيسي';
    }

    const userAgent = device.user_agent?.toLowerCase() || '';
    const deviceType = device.device_type?.toLowerCase() || '';
    const platform = device.platform?.toLowerCase() || '';
    const system = device.system?.toLowerCase() || '';

    // تحديد نوع الجهاز بناءً على user agent ومعلومات النظام
    if (userAgent.includes('iphone') || (userAgent.includes('mobile') && userAgent.includes('safari') && platform.includes('ios'))) {
      return 'iPhone';
    } else if (userAgent.includes('ipad') || platform.includes('ipad')) {
      return 'iPad';
    } else if (userAgent.includes('android') && userAgent.includes('mobile')) {
      return 'هاتف Android';
    } else if (userAgent.includes('android') && (userAgent.includes('tablet') || !userAgent.includes('mobile'))) {
      return 'جهاز Android لوحي';
    } else if (deviceType.includes('هاتف') || deviceType.includes('mobile') || userAgent.includes('mobile')) {
      return 'هاتف ذكي';
    } else if (deviceType.includes('لوحي') || deviceType.includes('tablet') || userAgent.includes('tablet')) {
      return 'جهاز لوحي';
    } else if (system.includes('windows') || userAgent.includes('windows')) {
      return 'كمبيوتر Windows';
    } else if (system.includes('mac') || userAgent.includes('macintosh') || userAgent.includes('mac os')) {
      return 'كمبيوتر Mac';
    } else if (system.includes('linux') || userAgent.includes('linux')) {
      return 'كمبيوتر Linux';
    } else if (userAgent.includes('chrome') || userAgent.includes('firefox') || userAgent.includes('safari') || userAgent.includes('edge')) {
      return 'كمبيوتر مكتبي';
    }

    return 'جهاز غير محدد';
  };



  const getDeviceIcon = () => {
    if (!device) return <FaDesktop />;

    if (device.is_main_server) {
      return <FaServer className="text-blue-600 dark:text-blue-400" />;
    }

    const userAgent = device.user_agent?.toLowerCase() || '';
    const deviceType = device.device_type?.toLowerCase() || '';
    const platform = device.platform?.toLowerCase() || '';

    // تحديد الأيقونة بناءً على نوع الجهاز الفعلي
    if (userAgent.includes('iphone') || (userAgent.includes('mobile') && platform.includes('ios'))) {
      return <FaMobile className="text-green-600 dark:text-green-400" />;
    } else if (userAgent.includes('ipad') || platform.includes('ipad')) {
      return <FaTabletAlt className="text-purple-600 dark:text-purple-400" />;
    } else if (userAgent.includes('android') && userAgent.includes('mobile')) {
      return <FaMobile className="text-green-600 dark:text-green-400" />;
    } else if (userAgent.includes('android') && (userAgent.includes('tablet') || !userAgent.includes('mobile'))) {
      return <FaTabletAlt className="text-purple-600 dark:text-purple-400" />;
    } else if (deviceType.includes('هاتف') || deviceType.includes('mobile') || userAgent.includes('mobile')) {
      return <FaMobile className="text-green-600 dark:text-green-400" />;
    } else if (deviceType.includes('لوحي') || deviceType.includes('tablet') || userAgent.includes('tablet')) {
      return <FaTabletAlt className="text-purple-600 dark:text-purple-400" />;
    }

    return <FaDesktop className="text-gray-600 dark:text-gray-400" />;
  };

  const getStatusBadge = () => {
    if (!device) return null;

    // استخدام البيانات المحسنة إذا كانت متوفرة
    const currentDevice = enhancedDevice || device;

    const statusConfig = {
      online: { color: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300', text: 'متصل' },
      recently_active: { color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300', text: 'نشط مؤخراً' },
      offline: { color: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300', text: 'غير متصل' }
    };

    const config = statusConfig[currentDevice.status as keyof typeof statusConfig] || statusConfig.offline;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (error) {
      console.error('فشل في النسخ:', error);
    }
  };

  const formatFingerprint = (fingerprint: string) => {
    if (!fingerprint) return 'غير متوفر';
    return showSensitiveData ? fingerprint : `${fingerprint.slice(0, 8)}...${fingerprint.slice(-4)}`;
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'غير محدد';

    try {
      // استخدام خدمة التاريخ الموجودة في المشروع لعرض التاريخ والوقت
      return formatDateTime(dateString, 'datetime') || 'غير محدد';
    } catch (error) {
      // fallback إذا فشلت خدمة التاريخ
      try {
        return new Date(dateString).toLocaleString('ar-SA', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      } catch {
        return dateString;
      }
    }
  };



  const exportHistoryToExcel = () => {
    if (fingerprintHistory.length === 0) {
      alert('⚠️ لا توجد بيانات للتصدير\nيرجى التأكد من وجود سجلات وصول للجهاز');
      return;
    }

    console.log(`📊 بدء تصدير سجل الوصول إلى Excel للجهاز: ${displayDevice.hostname}`);
    console.log(`📈 عدد السجلات: ${fingerprintHistory.length}`);

    // إنشاء بيانات CSV
    const headers = ['نوع الحدث', 'عنوان IP', 'التاريخ والوقت', 'تفاصيل إضافية'];
    const csvData = fingerprintHistory.map(entry => [
      entry.event_type === 'created' ? 'إنشاء' : entry.event_type === 'accessed' ? 'وصول' : entry.event_type === 'updated' ? 'تحديث' : entry.event_type,
      entry.ip_address || 'غير محدد',
      entry.created_at ? formatDateTime(entry.created_at, 'datetime') || 'غير محدد' : 'غير محدد',
      entry.event_data ? (typeof entry.event_data === 'object' ? JSON.stringify(entry.event_data) : entry.event_data) : 'لا توجد تفاصيل'
    ]);

    // إضافة معلومات الجهاز في البداية
    const deviceInfo = [
      ['معلومات الجهاز'],
      ['اسم الجهاز', displayDevice.hostname],
      ['معرف الجهاز', displayDevice.device_id],
      ['عنوان IP', displayDevice.client_ip],
      ['نوع الجهاز', getActualDeviceType()],
      ['النظام', displayDevice.system || 'غير محدد'],
      ['المتصفح', displayDevice.browser || 'غير محدد'],
      ['إجمالي مرات الوصول', displayDevice.access_count || 0],
      [''], // سطر فارغ
      ['سجل الوصول'],
      headers
    ];

    const allData = [...deviceInfo, ...csvData];

    // تحويل إلى CSV
    const csvContent = allData.map(row =>
      row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
    ).join('\n');

    // إنشاء ملف للتحميل
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `device-access-log-${displayDevice.hostname}-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    console.log('✅ تم تصدير سجل الوصول إلى Excel بنجاح');
    // عرض رسالة نجاح مؤقتة
    setTimeout(() => {
      alert('✅ تم تصدير سجل الوصول إلى Excel بنجاح\n📁 تحقق من مجلد التحميلات');
    }, 100);
  };

  const clearAccessHistory = async () => {
    if (!device?.device_id || device.is_main_server) {
      alert('لا يمكن تنظيف سجل الخادم الرئيسي');
      return;
    }

    const confirmClear = window.confirm(
      `هل أنت متأكد من تنظيف سجل الوصول للجهاز "${displayDevice.hostname}"؟\n\n` +
      `سيتم حذف ${fingerprintHistory.length} سجل وصول.\n` +
      `هذا الإجراء لا يمكن التراجع عنه.\n\n` +
      `ملاحظة: سيتم الاحتفاظ ببيانات البصمة الأساسية، وسيتم حذف سجل الأنشطة فقط.`
    );

    if (!confirmClear) return;

    try {
      const response = await api.delete(`/api/device-fingerprints/${device.device_id}/history`);
      if (response.data?.success) {
        setFingerprintHistory([]);
        // إعادة تعيين الصفحة إلى الأولى
        setHistoryCurrentPage(1);
        alert('✅ تم تنظيف سجل الوصول بنجاح\n📋 تم الاحتفاظ ببيانات البصمة الأساسية');
      } else {
        alert('❌ فشل في تنظيف سجل الوصول: ' + (response.data?.message || 'خطأ غير معروف'));
      }
    } catch (error) {
      console.error('خطأ في تنظيف سجل الوصول:', error);
      alert('❌ حدث خطأ أثناء تنظيف سجل الوصول\nيرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني');
    }
  };

  if (!device) return null;

  // ✅ استخدام الجهاز المحسن إذا كان متوفراً، وإلا استخدام الجهاز الأصلي - مع تحديث فوري
  const displayDevice = enhancedDevice || device;

  // دالة تصدير PDF من الخلفية (متقدم) مع مؤشر تحميل
  const exportHistoryToServerPDF = async () => {
    try {
      setIsExportingPDF(true);
      console.log('🚀 بدء تصدير PDF متقدم من الخلفية...');

      if (!displayDevice?.device_id) {
        alert('❌ معرف الجهاز غير متوفر');
        return;
      }

      // استخدام خدمة PDF الخلفية المتقدمة
      await exportDeviceAccessLogFromServer(displayDevice.device_id, {
        config: {
          company_name: 'نظام SmartPOS المتقدم',
          theme: 'light',
          include_header: true,
          include_footer: true,
          page_size: 'A4',
          orientation: 'portrait'
        }
      });

      console.log('✅ تم تصدير PDF متقدم بنجاح من الخلفية');

    } catch (error) {
      console.error('❌ خطأ في تصدير PDF المتقدم:', error);

      // رسالة خطأ مفصلة
      let errorMessage = 'حدث خطأ أثناء تصدير PDF المتقدم';
      if (error instanceof Error) {
        errorMessage += `\n${error.message}`;
      }

      alert(`❌ ${errorMessage}\n\nيمكنك استخدام "PDF عادي" كبديل`);
    } finally {
      setIsExportingPDF(false);
    }
  };

  return (
    <>
    <Modal isOpen={isOpen} onClose={onClose} title="تفاصيل الجهاز" size="xl" zIndex="high">
      <div className="space-y-6">
        {/* Header with device info */}
        <div className="flex items-start space-x-4 space-x-reverse p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <div className="flex-shrink-0 text-3xl">
            {getDeviceIcon()}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
                {displayDevice.hostname}
              </h3>
              <div className="flex items-center gap-2">
                {getStatusBadge()}
                {/* مستوى الأمان للأجهزة البعيدة */}
                {!displayDevice.is_main_server && (
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    displayDevice.is_advanced_fingerprint && !displayDevice.requires_approval ?
                      'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                      displayDevice.requires_approval ?
                        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                        'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                  }`}>
                    <FaShieldAlt className="ml-1" />
                    {displayDevice.is_advanced_fingerprint && !displayDevice.requires_approval ? 'أمان عالي' :
                     displayDevice.requires_approval ? 'أمان متوسط' : 'أمان أساسي'}
                  </span>
                )}

                {/* أزرار الإجراءات للأجهزة البعيدة فقط */}
                {!displayDevice.is_main_server && (
                  <div className="flex gap-2 mr-4">
                    {/* زر الحذف متاح دائماً للأجهزة البعيدة */}
                    <button
                      onClick={() => setShowDeleteModal(true)}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium transition-colors flex items-center gap-1.5"
                      title="حذف الجهاز نهائياً من النظام"
                    >
                      <Trash2 className="h-3 w-3" />
                      حذف نهائي
                    </button>
                    {onBlock && (
                      <button
                        onClick={() => onBlock(device!)}
                        className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium transition-colors flex items-center gap-1.5"
                        title="حظر الجهاز من الوصول"
                      >
                        <Ban className="h-3 w-3" />
                        حظر
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {displayDevice.client_ip} • {displayDevice.device_type}
            </p>
            <div className="flex items-center mt-2 space-x-4 space-x-reverse">
              {displayDevice.is_advanced_fingerprint && (
                <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                  <FaFingerprint className="ml-1" />
                  بصمة متقدمة
                </span>
              )}
              {displayDevice.is_main_server && (
                <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                  <FaServer className="ml-1" />
                  خادم رئيسي
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-600">
          <nav className="-mb-px flex space-x-8 space-x-reverse">
            <button
              onClick={() => setActiveTab('basic')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'basic'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <FaInfoCircle className="inline ml-2" />
              معلومات أساسية
            </button>

            {/* تبويبات البصمة وسجل الوصول للأجهزة البعيدة فقط */}
            {!displayDevice.is_main_server && (displayDevice.is_advanced_fingerprint || fingerprintData) && (
              <button
                onClick={() => setActiveTab('fingerprint')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'fingerprint'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <FaFingerprint className="inline ml-2" />
                بصمة الجهاز
              </button>
            )}

            {!displayDevice.is_main_server && (displayDevice.is_advanced_fingerprint || fingerprintData) && (
              <button
                onClick={() => {
                  setActiveTab('history');
                  setHistoryCurrentPage(1); // إعادة تعيين الصفحة عند تغيير التبويب
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'history'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <FaHistory className="inline ml-2" />
                سجل الوصول
              </button>
            )}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'basic' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* معلومات الجهاز */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
                  <FaDesktop className="ml-2" />
                  معلومات الجهاز
                </h4>
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4 space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">معرف الجهاز:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100 font-mono break-all">{displayDevice.device_id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">اسم الجهاز:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">{displayDevice.hostname || 'غير محدد'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">النظام:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">{displayDevice.system || 'غير محدد'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">المنصة:</span>
                    <span className="text-xs text-gray-900 dark:text-gray-100 break-all">{displayDevice.platform || 'غير محدد'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">المتصفح:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">{displayDevice.browser || 'غير محدد'}</span>
                  </div>
                  {displayDevice.machine && (
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">معمارية الجهاز:</span>
                      <span className="text-sm text-gray-900 dark:text-gray-100">{displayDevice.machine}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">نوع الجهاز:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100 font-medium">{getActualDeviceType()}</span>
                  </div>
                  {!displayDevice.is_main_server && (
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">مستوى الأمان:</span>
                      <span className={`text-sm font-medium ${displayDevice.is_advanced_fingerprint ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}`}>
                        {displayDevice.is_advanced_fingerprint ? 'بصمة متقدمة' : 'بصمة أساسية'}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">حالة الاتصال:</span>
                    <span className={`text-sm font-medium ${
                      displayDevice.status === 'online' ? 'text-green-600 dark:text-green-400' :
                      displayDevice.status === 'recently_active' ? 'text-yellow-600 dark:text-yellow-400' :
                      'text-gray-600 dark:text-gray-400'
                    }`}>
                      {displayDevice.status === 'online' ? 'متصل' :
                       displayDevice.status === 'recently_active' ? 'نشط مؤخراً' :
                       displayDevice.status === 'offline' ? 'غير متصل' :
                       displayDevice.status}
                    </span>
                  </div>
                </div>
              </div>

              {/* معلومات الاتصال */}
              <div className="space-y-4">
                <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
                  <FaNetworkWired className="ml-2" />
                  معلومات الاتصال
                </h4>
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4 space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">عنوان IP:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100 font-mono">{displayDevice.client_ip || 'غير محدد'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">نوع الوصول:</span>
                    <span className={`text-sm font-medium ${displayDevice.is_local_access ? 'text-green-600 dark:text-green-400' : 'text-orange-600 dark:text-orange-400'}`}>
                      {displayDevice.is_local_access ? 'شبكة محلية' : 'شبكة خارجية'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">أول وصول:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">{formatDate(displayDevice.first_access)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">آخر وصول:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">{formatDate(displayDevice.last_access)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">عدد مرات الوصول:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100 font-semibold">{displayDevice.access_count || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">حالة الموافقة:</span>
                    <span className={`text-sm font-medium ${displayDevice.requires_approval ? 'text-yellow-600 dark:text-yellow-400' : 'text-green-600 dark:text-green-400'}`}>
                      {displayDevice.requires_approval ? 'يتطلب موافقة' : 'معتمد'}
                    </span>
                  </div>
                  {displayDevice.current_user && (
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">المستخدم الحالي:</span>
                      <span className="text-sm text-gray-900 dark:text-gray-100 font-medium">{displayDevice.current_user}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">وقت الجلسة:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">
                      {displayDevice.last_access ? `منذ ${formatDateTime(displayDevice.last_access, 'datetime') || 'غير محدد'}` : 'غير محدد'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">مدة الجلسة:</span>
                    <span className="text-sm text-gray-900 dark:text-gray-100">
                      {displayDevice.session_duration_formatted ||
                       (displayDevice.first_access && displayDevice.last_access ?
                        `${Math.round((new Date(displayDevice.last_access).getTime() - new Date(displayDevice.first_access).getTime()) / (1000 * 60))} دقيقة` :
                        'غير محدد')}
                    </span>
                  </div>
                  {displayDevice.last_activity_formatted && (
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">آخر نشاط:</span>
                      <span className="text-sm text-gray-900 dark:text-gray-100">
                        {displayDevice.last_activity_formatted}
                      </span>
                    </div>
                  )}
                  {displayDevice.security_level_ar && (
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">مستوى الأمان المحسن:</span>
                      <span className={`text-sm font-medium ${
                        displayDevice.security_level === 'high' ? 'text-green-600 dark:text-green-400' :
                        displayDevice.security_level === 'medium' ? 'text-yellow-600 dark:text-yellow-400' :
                        'text-blue-600 dark:text-blue-400'
                      }`}>
                        {displayDevice.security_level_ar}
                      </span>
                    </div>
                  )}
                  {displayDevice.access_type_ar && (
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-500 dark:text-gray-400">نوع الوصول المحسن:</span>
                      <span className={`text-sm font-medium ${displayDevice.access_type === 'local' ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}`}>
                        {displayDevice.access_type_ar}
                      </span>
                    </div>
                  )}
                </div>
              </div>





              {/* معلومات User Agent */}
              {displayDevice.user_agent && (
                <div className="lg:col-span-2 space-y-4">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center">
                    <FaInfoCircle className="ml-2" />
                    معلومات المتصفح التفصيلية
                  </h4>
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4">
                    <div className="text-xs text-gray-600 dark:text-gray-400 break-all leading-relaxed">
                      {displayDevice.user_agent}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'fingerprint' && !displayDevice.is_main_server && (displayDevice.is_advanced_fingerprint || fingerprintData) && (
            <div className="space-y-6">
              {/* Toggle for sensitive data */}
              <div className="flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                <div className="flex items-center">
                  <FaShieldAlt className="text-yellow-600 dark:text-yellow-400 ml-2" />
                  <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    عرض البيانات الحساسة
                  </span>
                </div>
                <button
                  onClick={() => setShowSensitiveData(!showSensitiveData)}
                  className="flex items-center px-3 py-1 rounded-md text-sm font-medium bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-800 dark:text-yellow-100 dark:hover:bg-yellow-700 transition-colors"
                >
                  {showSensitiveData ? <FaEyeSlash className="ml-1" /> : <FaEye className="ml-1" />}
                  {showSensitiveData ? 'إخفاء' : 'عرض'}
                </button>
              </div>

              {isLoadingFingerprint ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  <span className="mr-3 text-gray-600 dark:text-gray-400">جاري تحميل بيانات البصمة...</span>
                </div>
              ) : fingerprintData ? (
                <div className="space-y-6">
                  {/* Basic Fingerprint Data */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">البصمة الأساسية</h4>
                      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4 space-y-3">
                        {[
                          { label: 'معرف البصمة', value: fingerprintData.fingerprint_id, field: 'fingerprint_id' },
                          { label: 'بصمة الأجهزة', value: fingerprintData.hardware_fingerprint, field: 'hardware' },
                          { label: 'بصمة التخزين', value: fingerprintData.storage_fingerprint, field: 'storage' },
                          { label: 'بصمة الشاشة', value: fingerprintData.screen_fingerprint, field: 'screen' },
                          { label: 'بصمة النظام', value: fingerprintData.system_fingerprint, field: 'system' },
                          { label: 'بصمة الشبكة', value: fingerprintData.network_fingerprint, field: 'network' }
                        ].map(({ label, value, field }) => value && (
                          <div key={field} className="flex justify-between items-center">
                            <span className="text-sm font-medium text-gray-500 dark:text-gray-400">{label}:</span>
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <span className="text-sm text-gray-900 dark:text-gray-100 font-mono">
                                {formatFingerprint(value)}
                              </span>
                              <button
                                onClick={() => copyToClipboard(value, field)}
                                className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                                title="نسخ"
                              >
                                {copiedField === field ? <FaCheck className="text-green-500" /> : <FaCopy />}
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Database Info */}
                    <div className="space-y-4">
                      <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">معلومات قاعدة البيانات</h4>
                      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4 space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">معرف قاعدة البيانات:</span>
                          <span className="text-sm text-gray-900 dark:text-gray-100 font-mono">{fingerprintData.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">تاريخ الإنشاء:</span>
                          <span className="text-sm text-gray-900 dark:text-gray-100">{formatDate(fingerprintData.created_at)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">آخر تحديث:</span>
                          <span className="text-sm text-gray-900 dark:text-gray-100">{formatDate(fingerprintData.updated_at)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">آخر ظهور:</span>
                          <span className="text-sm text-gray-900 dark:text-gray-100">{formatDate(fingerprintData.last_seen_at)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">آخر IP مسجل:</span>
                          <span className="text-sm text-gray-900 dark:text-gray-100 font-mono">{fingerprintData.last_ip || 'غير محدد'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">الحالة:</span>
                          <span className={`text-sm font-medium ${fingerprintData.is_active ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                            {fingerprintData.is_active ? 'نشط' : 'غير نشط'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">معتمد تلقائياً:</span>
                          <span className={`text-sm font-medium ${fingerprintData.auto_approved ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}`}>
                            {fingerprintData.auto_approved ? 'نعم' : 'لا'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Additional Information Sections */}
                  {(fingerprintData.device_info || fingerprintData.system_info || fingerprintData.browser_info || fingerprintData.screen_info) && (
                    <div className="space-y-4">
                      <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">معلومات تفصيلية إضافية</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {fingerprintData.device_info && (
                          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4">
                            <h5 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">معلومات الجهاز</h5>
                            <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap break-all">
                              {typeof fingerprintData.device_info === 'string'
                                ? fingerprintData.device_info
                                : JSON.stringify(fingerprintData.device_info, null, 2)}
                            </pre>
                          </div>
                        )}

                        {fingerprintData.system_info && (
                          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4">
                            <h5 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">معلومات النظام</h5>
                            <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap break-all">
                              {typeof fingerprintData.system_info === 'string'
                                ? fingerprintData.system_info
                                : JSON.stringify(fingerprintData.system_info, null, 2)}
                            </pre>
                          </div>
                        )}

                        {fingerprintData.browser_info && (
                          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4">
                            <h5 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">معلومات المتصفح</h5>
                            <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap break-all">
                              {typeof fingerprintData.browser_info === 'string'
                                ? fingerprintData.browser_info
                                : JSON.stringify(fingerprintData.browser_info, null, 2)}
                            </pre>
                          </div>
                        )}

                        {fingerprintData.screen_info && (
                          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4">
                            <h5 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">معلومات الشاشة</h5>
                            <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap break-all">
                              {typeof fingerprintData.screen_info === 'string'
                                ? fingerprintData.screen_info
                                : JSON.stringify(fingerprintData.screen_info, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Last User Agent */}
                  {fingerprintData.last_user_agent && (
                    <div className="space-y-4">
                      <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">آخر User Agent مسجل</h4>
                      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4">
                        <div className="text-xs text-gray-600 dark:text-gray-400 break-all leading-relaxed">
                          {fingerprintData.last_user_agent}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FaFingerprint className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    لا توجد بيانات بصمة متقدمة في قاعدة البيانات
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                    قد تكون البصمة محفوظة في ملف الأجهزة المتصلة فقط
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'history' && !displayDevice.is_main_server && (displayDevice.is_advanced_fingerprint || fingerprintData) && (() => {
            // ترتيب سجلات الوصول: fingerprint_created أولاً، ثم باقي السجلات حسب التاريخ
            const sortedHistory = [...fingerprintHistory].sort((a, b) => {
              // إعطاء أولوية لسجل fingerprint_created أو created
              const aIsCreation = a.event_type === 'fingerprint_created' || a.event_type === 'created';
              const bIsCreation = b.event_type === 'fingerprint_created' || b.event_type === 'created';

              if (aIsCreation && !bIsCreation) return -1;
              if (bIsCreation && !aIsCreation) return 1;

              // إذا كان كلاهما إنشاء أو كلاهما ليس كذلك، رتب حسب التاريخ (الأحدث أولاً)
              return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
            });

            // حساب pagination لسجل الوصول المرتب
            const totalHistoryPages = Math.ceil(sortedHistory.length / historyPageSize);
            const startIndex = (historyCurrentPage - 1) * historyPageSize;
            const endIndex = startIndex + historyPageSize;
            const paginatedHistory = sortedHistory.slice(startIndex, endIndex);

            return (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">سجل وصول البصمة</h4>
                  <div className="flex items-center gap-3">
                    {fingerprintHistory.length > 0 && (
                      <>
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {fingerprintHistory.length} حدث
                        </span>

                        {/* أزرار التصدير والتنظيف */}
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                            <button
                              onClick={exportHistoryToServerPDF}
                              disabled={isExportingPDF}
                              className={`px-3 py-1.5 text-xs font-medium rounded-md transition-colors flex items-center gap-1.5 border ${
                                isExportingPDF
                                  ? 'text-gray-500 bg-gray-100 border-gray-300 cursor-not-allowed'
                                  : 'text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 border-blue-200 dark:border-blue-800'
                              }`}
                              title={isExportingPDF ? "جاري تصدير PDF..." : "تصدير سجل الوصول إلى PDF متقدم (من الخلفية مع دعم أفضل للعربية)"}
                            >
                              {isExportingPDF ? (
                                <>
                                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-500"></div>
                                  <span>جاري التصدير...</span>
                                </>
                              ) : (
                                <>
                                  <FaFilePdf className="text-blue-600 dark:text-blue-400" />
                                  <span>PDF متقدم</span>
                                </>
                              )}
                            </button>

                            <button
                              onClick={exportHistoryToExcel}
                              className="px-3 py-1.5 text-xs font-medium text-green-700 dark:text-green-300 bg-white dark:bg-gray-600 rounded-md hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors flex items-center gap-1.5"
                              title="تصدير سجل الوصول إلى Excel للتحليل"
                            >
                              <FaFileExcel className="text-green-600 dark:text-green-400" />
                              <span>Excel</span>
                            </button>
                          </div>

                          <button
                            onClick={clearAccessHistory}
                            className="px-3 py-1.5 text-xs font-medium text-red-700 dark:text-red-300 bg-red-100 dark:bg-red-900/20 rounded-md hover:bg-red-200 dark:hover:bg-red-900/40 transition-colors flex items-center gap-1.5 border border-red-200 dark:border-red-800"
                            title="تنظيف جميع سجلات الوصول (لا يمكن التراجع)"
                          >
                            <FaTrash className="text-red-600 dark:text-red-400" />
                            <span>تنظيف السجل</span>
                          </button>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {fingerprintHistory.length > 0 ? (
                  <div className="space-y-4">
                    {/* إحصائيات سريعة */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4 text-center">
                        <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                          {fingerprintHistory.filter(h =>
                            h.event_type === 'device_access' ||
                            h.event_type === 'accessed' ||
                            h.event_type === 'fingerprint_updated'
                          ).length}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">مرات الوصول</div>
                      </div>
                      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4 text-center">
                        <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                          {fingerprintHistory.filter(h => h.event_type === 'fingerprint_created' || h.event_type === 'created').length}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">مرات الإنشاء</div>
                      </div>
                      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-4 text-center">
                        <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                          {new Set(fingerprintHistory.map(h => h.ip_address).filter(Boolean)).size}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">عناوين IP مختلفة</div>
                      </div>
                    </div>

                    {/* جدول السجل مع pagination */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden">
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                          <thead className="bg-gray-50 dark:bg-gray-700">
                            <tr>
                              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                نوع الحدث
                              </th>
                              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                عنوان IP
                              </th>
                              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                التاريخ والوقت
                              </th>
                              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                تفاصيل إضافية
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                            {paginatedHistory.map((entry, index) => (
                              <tr key={startIndex + index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                    entry.event_type === 'fingerprint_created' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                                    entry.event_type === 'device_access' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                                    entry.event_type === 'fingerprint_updated' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                                    entry.event_type === 'device_approved' ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300' :
                                    entry.event_type === 'device_blocked' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                                    entry.event_type === 'created' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                                    entry.event_type === 'accessed' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                                    entry.event_type === 'updated' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                                    'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                                  }`}>
                                    {entry.event_type === 'fingerprint_created' ? 'إنشاء البصمة' :
                                     entry.event_type === 'device_access' ? 'وصول الجهاز' :
                                     entry.event_type === 'fingerprint_updated' ? 'تحديث البصمة' :
                                     entry.event_type === 'device_approved' ? 'موافقة على الجهاز' :
                                     entry.event_type === 'device_blocked' ? 'حظر الجهاز' :
                                     entry.event_type === 'created' ? 'إنشاء' :
                                     entry.event_type === 'accessed' ? 'وصول' :
                                     entry.event_type === 'updated' ? 'تحديث' :
                                     entry.event_type}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 font-mono">
                                  {entry.ip_address || 'غير محدد'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                  {entry.created_at ? formatDateTime(entry.created_at, 'datetime') || 'غير محدد' : 'غير محدد'}
                                </td>
                                <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                  {entry.event_data && typeof entry.event_data === 'object' ? (
                                    <div className="max-w-xs">
                                      <details className="cursor-pointer">
                                        <summary className="text-blue-600 dark:text-blue-400 hover:underline">
                                          عرض التفاصيل
                                        </summary>
                                        <pre className="mt-2 text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap break-all">
                                          {JSON.stringify(entry.event_data, null, 2)}
                                        </pre>
                                      </details>
                                    </div>
                                  ) : entry.event_data ? (
                                    <span className="text-xs text-gray-600 dark:text-gray-400">
                                      {String(entry.event_data).slice(0, 50)}...
                                    </span>
                                  ) : (
                                    <span className="text-xs text-gray-400">لا توجد تفاصيل</span>
                                  )}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>

                      {/* شريط التنقل بين الصفحات */}
                      {totalHistoryPages > 1 && (
                        <div className="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-600 sm:px-6">
                          <div className="flex items-center justify-between">
                            <div className="flex-1 flex justify-between sm:hidden">
                              <button
                                onClick={() => setHistoryCurrentPage(Math.max(1, historyCurrentPage - 1))}
                                disabled={historyCurrentPage === 1}
                                className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                السابق
                              </button>
                              <button
                                onClick={() => setHistoryCurrentPage(Math.min(totalHistoryPages, historyCurrentPage + 1))}
                                disabled={historyCurrentPage === totalHistoryPages}
                                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                التالي
                              </button>
                            </div>
                            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                              <div>
                                <p className="text-sm text-gray-700 dark:text-gray-300">
                                  عرض{' '}
                                  <span className="font-medium">{startIndex + 1}</span>
                                  {' '}إلى{' '}
                                  <span className="font-medium">{Math.min(endIndex, fingerprintHistory.length)}</span>
                                  {' '}من{' '}
                                  <span className="font-medium">{fingerprintHistory.length}</span>
                                  {' '}نتيجة
                                </p>
                              </div>
                              <div>
                                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                  <button
                                    onClick={() => setHistoryCurrentPage(Math.max(1, historyCurrentPage - 1))}
                                    disabled={historyCurrentPage === 1}
                                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                                  >
                                    <span className="sr-only">السابق</span>
                                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                      <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                  </button>

                                  {Array.from({ length: totalHistoryPages }, (_, i) => i + 1).map((page) => (
                                    <button
                                      key={page}
                                      onClick={() => setHistoryCurrentPage(page)}
                                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                        page === historyCurrentPage
                                          ? 'z-10 bg-primary-50 dark:bg-primary-900/20 border-primary-500 text-primary-600 dark:text-primary-400'
                                          : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                                      }`}
                                    >
                                      {page}
                                    </button>
                                  ))}

                                  <button
                                    onClick={() => setHistoryCurrentPage(Math.min(totalHistoryPages, historyCurrentPage + 1))}
                                    disabled={historyCurrentPage === totalHistoryPages}
                                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                                  >
                                    <span className="sr-only">التالي</span>
                                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                    </svg>
                                  </button>
                                </nav>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FaHistory className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-gray-600 dark:text-gray-400">لا يوجد سجل وصول متاح</p>
                    <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                      سيتم عرض سجل الأنشطة هنا عند توفر البيانات
                    </p>
                  </div>
                )}
              </div>
            );
          })()}
        </div>
      </div>
    </Modal>

    {/* نافذة تأكيد الحذف - باستخدام portal لضمان الظهور فوق جميع المكونات */}
    {createPortal(
      <DeleteDeviceModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteDevice}
        device={device}
        isLoading={isDeleting}
      />,
      document.body
    )}
  </>
  );
};

export default DeviceDetailsModal;
