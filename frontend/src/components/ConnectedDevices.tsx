/**
 * مكون عرض الأجهزة المتصلة بالشبكة
 */

import React, { useState, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import {
  FaServer,
  FaDesktop,
  FaLaptop,
  FaMobile,
  FaNetworkWired,
  FaCircle,
  FaSync,
  FaWifi,
  FaInfoCircle,
  FaBan,
  FaExclamationTriangle,
  FaTrash,
  FaCheckCircle
} from 'react-icons/fa';
import { CheckCircle, Clock, XCircle, Zap, User } from './ui/icons';
import { useConnectedDevices } from '../hooks/useConnectedDevices';
import { safeFormatDate } from '../services/dateTimeService';
import api from '../lib/axios';
import ConfirmModal from './ConfirmModal';
import DeviceDetailsModal from './DeviceDetailsModal';
import DeleteAllDevicesModal from './DeleteAllDevicesModal';
import { urlDetectionService } from '../services/urlDetectionService';

interface ConnectedDevicesProps {
  /** عرض الملخص فقط أم التفاصيل الكاملة */
  showSummaryOnly?: boolean;
  /** فئات CSS إضافية */
  className?: string;
}

const ConnectedDevices: React.FC<ConnectedDevicesProps> = ({
  showSummaryOnly = false,
  className = '',
}) => {
  const [useRealTime, setUseRealTime] = useState(true); // تفعيل التحديث المباشر لرؤية النظام الجديد
  const { devices, summary, isLoading, error, currentClientIp, lastUpdated, refreshDevices } = useConnectedDevices(useRealTime);
  // تم حذف selectedDevice - استخدام النافذة الكاملة فقط
  const [selectedDeviceForDetails, setSelectedDeviceForDetails] = useState<any>(null);
  const [enhancedDevicesData, setEnhancedDevicesData] = useState<{[key: string]: any}>({});
  const [updatingDevices, setUpdatingDevices] = useState<Set<string>>(new Set());
  const [dynamicServerAddress, setDynamicServerAddress] = useState<string>('*************');

  // تنظيف cache البيانات القديمة
  const cleanupOldCache = useCallback(() => {
    const now = Date.now();
    const maxAge = 300000; // 5 دقائق

    setEnhancedDevicesData(prev => {
      const cleaned = { ...prev };
      let hasChanges = false;

      Object.keys(cleaned).forEach(deviceId => {
        const data = cleaned[deviceId];
        if (data.lastUpdated && (now - data.lastUpdated) > maxAge) {
          delete cleaned[deviceId];
          hasChanges = true;
          console.log(`🧹 [CACHE] تنظيف البيانات القديمة للجهاز ${deviceId}`);
        }
      });

      return hasChanges ? cleaned : prev;
    });
  }, []);

  // ✅ دالة محسنة للحصول على البيانات المحسنة للجهاز مع تحديث فوري
  const getEnhancedDevice = (device: any) => {
    const enhanced = enhancedDevicesData[device.device_id];
    if (enhanced) {
      return {
        ...device,
        // ✅ البيانات الحية من المصدر الأساسي (أولوية للبيانات الحديثة)
        status: device.status, // ✅ الحالة الحديثة من WebSocket
        status_ar: enhanced.status_ar || device.status_ar,
        current_user: device.current_user, // ✅ المستخدم الحالي الحديث
        access_count: device.access_count, // ✅ عدد الوصول الحديث
        last_access: device.last_access, // ✅ آخر وصول حديث
        first_access: enhanced.first_access || device.first_access,
        session_duration_minutes: enhanced.session_duration_minutes,
        session_duration_formatted: enhanced.session_duration_formatted,
        last_activity_formatted: enhanced.last_activity_formatted,

        // البيانات الثابتة من قاعدة البيانات
        hardware_fingerprint: enhanced.hardware_fingerprint || device.hardware_fingerprint,
        storage_fingerprint: enhanced.storage_fingerprint || device.storage_fingerprint,
        screen_fingerprint: enhanced.screen_fingerprint || device.screen_fingerprint,
        system_fingerprint: enhanced.system_fingerprint || device.system_fingerprint,
        is_advanced_fingerprint: enhanced.is_advanced_fingerprint !== undefined ? enhanced.is_advanced_fingerprint : device.is_advanced_fingerprint,

        // معلومات محسنة
        security_level: enhanced.security_level,
        security_level_ar: enhanced.security_level_ar,
        access_type: enhanced.access_type,
        access_type_ar: enhanced.access_type_ar,
        requires_approval: enhanced.requires_approval !== undefined ? enhanced.requires_approval : device.requires_approval,

        // معلومات إضافية
        dataSource: enhanced.dataSource || 'original'
      };
    }
    return device;
  };

  // تحميل البيانات المحسنة للأجهزة البعيدة (مع نظام cache ذكي)
  const loadEnhancedDeviceData = async (deviceId: string, forceRefresh = false) => {
    if (!deviceId) return;

    // فحص البيانات المحفوظة مع التحقق من صحتها
    const existingData = enhancedDevicesData[deviceId];
    if (!forceRefresh && existingData) {
      // التحقق من عمر البيانات
      const dataAge = Date.now() - (existingData.lastUpdated || 0);
      if (dataAge < 60000) { // دقيقة واحدة للبيانات الحية
        console.log(`📊 [CACHE] استخدام البيانات المحفوظة للجهاز ${deviceId} (عمر: ${Math.round(dataAge/1000)}s)`);
        return;
      }
    }

    // منع الطلبات المتكررة خلال فترة قصيرة مع cache محسن
    const cacheKey = `enhanced_${deviceId}`;
    const lastFetch = sessionStorage.getItem(cacheKey);
    const now = Date.now();

    if (!forceRefresh && lastFetch && (now - parseInt(lastFetch)) < 30000) { // 30 ثانية
      console.log(`⏳ [CACHE] تجنب الطلب المتكرر للجهاز ${deviceId}`);
      return;
    }

    try {
      // إضافة الجهاز إلى قائمة الأجهزة قيد التحديث
      setUpdatingDevices(prev => new Set(prev).add(deviceId));

      // محاولة الحصول على البيانات المدمجة أولاً
      const integratedResponse = await api.get(`/api/device-fingerprints/${deviceId}/integrated-data`);

      if (integratedResponse.data?.success && integratedResponse.data.integrated_data) {
        console.log(`🔄 [INTEGRATED] تم الحصول على البيانات المدمجة للجهاز ${deviceId}`);

        // إضافة معلومات cache للبيانات المدمجة
        const enhancedData = {
          ...integratedResponse.data.integrated_data,
          lastUpdated: now,
          cacheValid: true,
          dataSource: 'integrated'
        };

        setEnhancedDevicesData(prev => ({
          ...prev,
          [deviceId]: enhancedData
        }));

        // حفظ وقت آخر تحديث
        sessionStorage.setItem(cacheKey, now.toString());
        sessionStorage.setItem(`${cacheKey}_validity`, 'true');
        console.log(`✅ تم تحديث البيانات المدمجة للجهاز ${deviceId}`);

      } else {
        // إذا فشلت البيانات المدمجة، استخدم الطريقة القديمة
        console.log(`🔄 [FALLBACK] استخدام الطريقة القديمة للجهاز ${deviceId}`);

        const response = await api.get(`/api/device-fingerprints/${deviceId}/complete-details`);
        if (response.data?.success && response.data.combined_info) {
          // إضافة معلومات cache للبيانات
          const enhancedData = {
            ...response.data.combined_info,
            lastUpdated: now,
            cacheValid: true,
            dataSource: 'legacy'
          };

          setEnhancedDevicesData(prev => ({
            ...prev,
            [deviceId]: enhancedData
          }));

          // حفظ وقت آخر تحديث مع معلومات إضافية
          sessionStorage.setItem(cacheKey, now.toString());
          sessionStorage.setItem(`${cacheKey}_validity`, 'true');
          console.log(`✅ تم تحديث البيانات المحسنة للجهاز ${deviceId} بالطريقة القديمة`);
        }
      }
    } catch (error) {
      console.warn(`⚠️ فشل في تحميل البيانات المحسنة للجهاز ${deviceId}:`, error);
    } finally {
      // إزالة الجهاز من قائمة الأجهزة قيد التحديث
      setUpdatingDevices(prev => {
        const newSet = new Set(prev);
        newSet.delete(deviceId);
        return newSet;
      });
    }
  };

  // تحميل البيانات المحسنة للأجهزة البعيدة عند تحديث قائمة الأجهزة (مرة واحدة فقط)
  useEffect(() => {
    if (devices && devices.length > 0 && !isLoading) {
      devices.forEach(device => {
        // تحميل البيانات المحسنة للأجهزة البعيدة فقط إذا لم تكن محملة مسبقاً
        if (!device.is_main_server && !enhancedDevicesData[device.device_id] && !updatingDevices.has(device.device_id)) {
          loadEnhancedDeviceData(device.device_id, false); // بدون إجبار التحديث
        }
      });
    }
  }, [devices, isLoading]);

  // ✅ مستمع للأحداث المخصصة للتحديث الفوري في بطاقات الأجهزة
  useEffect(() => {
    const handleDevicesUpdate = (event: CustomEvent) => {
      console.log('🔄 ConnectedDevices: تم استلام حدث تحديث الأجهزة:', event.detail);

      // تحديث البيانات المحسنة للأجهزة المحدثة
      if (event.detail?.devices) {
        const updatedDevices = event.detail.devices;

        // تحديث البيانات المحسنة لكل جهاز محدث
        setEnhancedDevicesData(prev => {
          const newData = { ...prev };

          updatedDevices.forEach((updatedDevice: any) => {
            if (newData[updatedDevice.device_id]) {
              // دمج البيانات الجديدة مع البيانات المحسنة الموجودة
              newData[updatedDevice.device_id] = {
                ...newData[updatedDevice.device_id],
                ...updatedDevice,
                lastUpdated: Date.now(),
                cacheValid: true
              };
              console.log(`✅ تم تحديث البيانات المحسنة للجهاز: ${updatedDevice.device_id}`);
            }
          });

          return newData;
        });
      }
    };

    const handleDeviceUserUpdate = (event: CustomEvent) => {
      console.log('🔄 ConnectedDevices: تم استلام حدث تحديث المستخدم:', event.detail);

      // إجبار تحديث البيانات بعد تأخير قصير
      setTimeout(() => {
        console.log('🔄 ConnectedDevices: إجبار تحديث البيانات بعد تغيير المستخدم');
        refreshDevices();
      }, 200); // تأخير قصير جداً
    };

    // ✅ مستمع لحدث حذف الجهاز
    const handleDeviceDeleted = (event: CustomEvent) => {
      console.log('🗑️ تم استلام حدث حذف الجهاز:', event.detail);

      // تحديث فوري لقائمة الأجهزة
      refreshDevices();

      // إزالة الجهاز من البيانات المحسنة
      if (event.detail?.device_id) {
        setEnhancedDevicesData(prev => {
          const updated = { ...prev };
          delete updated[event.detail.device_id];
          return updated;
        });
      }
    };

    // ✅ مستمع لحدث التحديث القسري
    const handleForceRefresh = (event: CustomEvent) => {
      console.log('🔄 تم استلام حدث التحديث القسري:', event.detail);

      // تحديث فوري مع تنظيف البيانات
      refreshDevices();

      // تنظيف البيانات المحسنة للجهاز المحذوف
      if (event.detail?.device_id) {
        setEnhancedDevicesData(prev => {
          const updated = { ...prev };
          delete updated[event.detail.device_id];
          return updated;
        });
      }
    };

    // ✅ مستمع لحدث تحديث قائمة الأجهزة
    const handleRefreshDevicesList = (event: CustomEvent) => {
      console.log('📋 تم استلام حدث تحديث قائمة الأجهزة:', event.detail);

      // تحديث فوري للقائمة
      refreshDevices();

      // تنظيف البيانات المحسنة
      if (event.detail?.device_id) {
        setEnhancedDevicesData(prev => {
          const updated = { ...prev };
          delete updated[event.detail.device_id];
          return updated;
        });
      }
    };

    // ✅ مستمع لحدث إزالة الجهاز من الواجهة فوراً
    const handleRemoveDeviceFromUI = (event: CustomEvent) => {
      console.log('🗑️ إزالة فورية للجهاز من الواجهة:', event.detail);

      if (event.detail?.device_id) {
        // إزالة الجهاز من البيانات المحسنة فوراً
        setEnhancedDevicesData(prev => {
          const updated = { ...prev };
          delete updated[event.detail.device_id];
          return updated;
        });

        // تحديث القائمة فوراً
        refreshDevices();
      }
    };

    // إضافة مستمعي الأحداث
    window.addEventListener('devicesUpdated', handleDevicesUpdate as EventListener);
    window.addEventListener('deviceUserUpdated', handleDeviceUserUpdate as EventListener);
    window.addEventListener('deviceDeleted', handleDeviceDeleted as EventListener);
    window.addEventListener('forceDevicesRefresh', handleForceRefresh as EventListener);
    window.addEventListener('refreshDevicesList', handleRefreshDevicesList as EventListener);
    window.addEventListener('removeDeviceFromUI', handleRemoveDeviceFromUI as EventListener);

    return () => {
      // إزالة مستمعي الأحداث
      window.removeEventListener('devicesUpdated', handleDevicesUpdate as EventListener);
      window.removeEventListener('deviceUserUpdated', handleDeviceUserUpdate as EventListener);
      window.removeEventListener('deviceDeleted', handleDeviceDeleted as EventListener);
      window.removeEventListener('forceDevicesRefresh', handleForceRefresh as EventListener);
      window.removeEventListener('refreshDevicesList', handleRefreshDevicesList as EventListener);
      window.removeEventListener('removeDeviceFromUI', handleRemoveDeviceFromUI as EventListener);
    };
  }, [refreshDevices]);

  // تنظيف cache دوري
  useEffect(() => {
    const interval = setInterval(cleanupOldCache, 60000); // كل دقيقة
    return () => clearInterval(interval);
  }, [cleanupOldCache]);

  // جلب العنوان الديناميكي للخادم
  useEffect(() => {
    const fetchDynamicAddress = async () => {
      try {
        const config = await urlDetectionService.getURLConfig();
        // استخراج العنوان من URL الخادم
        const url = new URL(config.backendURL);
        setDynamicServerAddress(url.hostname);
        console.log(`🌐 [ConnectedDevices] تم تحديث عنوان الخادم الديناميكي: ${url.hostname}`);
      } catch (error) {
        console.warn('⚠️ [ConnectedDevices] فشل في جلب العنوان الديناميكي:', error);
        // الاحتفاظ بالعنوان الافتراضي
      }
    };

    fetchDynamicAddress();
  }, []);

  // تم إزالة التحديث الدوري المنفصل لتجنب التضارب مع useConnectedDevices
  // البيانات المحسنة ستحدث مع التحديث الرئيسي فقط لتوحيد مصدر البيانات

  const [isBlocking, setIsBlocking] = useState(false);
  const [showBlockConfirm, setShowBlockConfirm] = useState(false);
  const [deviceToBlock, setDeviceToBlock] = useState<any>(null);

  const [isCleaningInactive, setIsCleaningInactive] = useState(false);
  const [isCleaningAll, setIsCleaningAll] = useState(false);
  const [showCleanupAllConfirm, setShowCleanupAllConfirm] = useState(false);

  // تم حذف تأثير scroll - يتم التعامل معه في النافذة الكاملة

  // دالة فتح نافذة تأكيد حظر الجهاز
  const openBlockConfirm = (device: any) => {
    setDeviceToBlock(device);
    setShowBlockConfirm(true);
    setSelectedDeviceForDetails(null); // إغلاق نافذة التفاصيل
  };

  // دالة حظر الجهاز
  const blockDevice = async () => {
    if (!deviceToBlock) return;

    try {
      setIsBlocking(true);
      await api.post('/api/device-security/block-device', {
        device_id: deviceToBlock.device_id,
        reason: "محظور بواسطة المدير"
      });

      // تحديث قائمة الأجهزة
      refreshDevices();
      setSelectedDeviceForDetails(null);
      setShowBlockConfirm(false);
      setDeviceToBlock(null);

    } catch (error) {
      console.error('خطأ في حظر الجهاز:', error);
    } finally {
      setIsBlocking(false);
    }
  };

  // ✅ دالة إلغاء حظر الجهاز
  const unblockDevice = async (deviceId: string) => {
    try {
      await api.post(`/api/device-security/unblock-device/${deviceId}`);
      // تحديث قائمة الأجهزة
      refreshDevices();
    } catch (error) {
      console.error('خطأ في إلغاء حظر الجهاز:', error);
    }
  };





  // دالة تنظيف الأجهزة غير النشطة
  const cleanupInactiveDevices = async () => {
    try {
      setIsCleaningInactive(true);

      // ✅ زيادة timeout لعملية التنظيف الذكي (60 ثانية)
      const response = await api.post('/api/settings/cleanup-inactive-devices', {}, {
        timeout: 60000 // 60 ثانية
      });

      if (response.data.success) {
        // تحديث قائمة الأجهزة
        refreshDevices();

        // عرض رسالة نجاح
        console.log('✅ تم تنظيف الأجهزة غير النشطة:', response.data.message);

        // إظهار تفاصيل العملية للمستخدم
        if (response.data.details) {
          console.log('📊 تفاصيل التنظيف:', response.data.details);
        }
      } else {
        console.error('❌ فشل في تنظيف الأجهزة غير النشطة:', response.data.message);
      }
    } catch (error: any) {
      console.error('❌ خطأ في تنظيف الأجهزة غير النشطة:', error);

      // معالجة خطأ timeout بشكل خاص
      if (error?.code === 'ECONNABORTED') {
        console.error('⏰ انتهت مهلة العملية - قد تكون العملية لا تزال قيد التنفيذ في الخادم');
        // محاولة تحديث البيانات بعد فترة
        setTimeout(() => {
          refreshDevices();
        }, 5000);
      }
    } finally {
      setIsCleaningInactive(false);
    }
  };

  // دالة تنظيف جميع الأجهزة مع الحفاظ على الخادم الرئيسي
  const cleanupAllDevices = async () => {
    try {
      setIsCleaningAll(true);
      const response = await api.post('/api/settings/cleanup-all-devices');

      if (response.data.success) {
        // تحديث قائمة الأجهزة
        refreshDevices();

        // عرض رسالة نجاح
        console.log('تم تنظيف جميع الأجهزة:', response.data.message);
      } else {
        console.error('فشل في تنظيف جميع الأجهزة:', response.data.message);
      }
    } catch (error) {
      console.error('خطأ في تنظيف جميع الأجهزة:', error);
    } finally {
      setIsCleaningAll(false);
    }
  };

  // الحصول على أيقونة الجهاز بناءً على النوع والنظام مع تحسينات بصرية
  const getDeviceIcon = (device: any) => {
    if (device.is_main_server) {
      return <FaServer className="text-blue-600 dark:text-blue-400 drop-shadow-lg" />;
    }

    const system = device.system?.toLowerCase() || '';
    const userAgent = device.user_agent?.toLowerCase() || '';
    const platform = device.platform?.toLowerCase() || '';

    // تحديد نوع الجهاز بناءً على معلومات أكثر تفصيلاً مع ألوان مميزة
    if (userAgent.includes('mobile') || userAgent.includes('iphone') || platform.includes('ios')) {
      return <FaMobile className="text-green-600 dark:text-green-400 drop-shadow-lg" />;
    } else if (userAgent.includes('ipad') || platform.includes('ipad')) {
      return <FaLaptop className="text-purple-600 dark:text-purple-400 drop-shadow-lg" />;
    } else if (userAgent.includes('android') || platform.includes('android')) {
      return <FaMobile className="text-green-600 dark:text-green-400 drop-shadow-lg" />;
    } else if (system.includes('windows') || platform.includes('windows')) {
      return <FaDesktop className="text-blue-600 dark:text-blue-400 drop-shadow-lg" />;
    } else if (system.includes('darwin') || system.includes('mac') || platform.includes('mac')) {
      return <FaLaptop className="text-gray-700 dark:text-gray-300 drop-shadow-lg" />;
    } else if (system.includes('linux')) {
      return <FaDesktop className="text-orange-600 dark:text-orange-400 drop-shadow-lg" />;
    } else {
      return <FaLaptop className="text-purple-600 dark:text-purple-400 drop-shadow-lg" />;
    }
  };

  // تم حذف دالة getStatusColor - كانت مستخدمة في النافذة العادية المحذوفة

  // تم حذف دالة getStatusText - كانت مستخدمة في النافذة العادية المحذوفة

  // تم حذف دالة getStatusIcon - كانت مستخدمة في النافذة العادية المحذوفة

  // تنسيق التاريخ باستخدام خدمة التاريخ الموحدة الآمنة
  const formatDate = (dateString: string | null | undefined) => {
    return safeFormatDate(dateString, 'غير معروف');
  };

  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <FaSync className="animate-spin text-2xl text-primary-600 dark:text-primary-400 ml-3" />
          <span className="text-gray-600 dark:text-gray-300">جاري تحميل بيانات الأجهزة...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 ${className}`}>
        <div className="text-center py-8">
          <FaInfoCircle className="text-4xl text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            خطأ في تحميل البيانات
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={refreshDevices}
            className="btn-primary flex items-center gap-2 mx-auto"
          >
            <FaSync />
            <span>إعادة المحاولة</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* ملخص الأجهزة */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <div className="bg-primary-100 dark:bg-primary-900/30 p-3 rounded-xl">
              <FaNetworkWired className="text-2xl text-primary-600 dark:text-primary-400" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                الأجهزة المتصلة
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                مراقبة الأجهزة المتصلة بالشبكة في الوقت الفعلي
              </p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            {lastUpdated && (
              <div className="text-right">
                <p className="text-xs text-gray-500 dark:text-gray-400">آخر تحديث</p>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {formatDate(lastUpdated.toISOString())}
                </p>
              </div>
            )}
            <div className="flex items-center gap-2">
              <button
                onClick={refreshDevices}
                className="btn-secondary-sm flex items-center gap-2"
                disabled={isLoading}
                title="تحديث البيانات يدوياً"
              >
                <FaSync className={isLoading ? 'animate-spin' : ''} />
                <span>تحديث</span>
              </button>

              <button
                onClick={() => setUseRealTime(!useRealTime)}
                className={`btn-outline-sm flex items-center gap-2 ${
                  useRealTime
                    ? '!bg-success-100 dark:!bg-success-900/20 !text-success-700 dark:!text-success-300 !border-success-300 dark:!border-success-600'
                    : '!bg-primary-100 dark:!bg-primary-900/20 !text-primary-700 dark:!text-primary-300 !border-primary-300 dark:!border-primary-600'
                }`}
                title={useRealTime ? 'تعطيل التحديث المباشر (توفير الموارد)' : 'تفعيل التحديث المباشر'}
              >
                <FaWifi className={useRealTime ? 'text-success-500' : 'text-primary-500'} />
                <span>{useRealTime ? 'مباشر' : 'يدوي'}</span>
              </button>

              <button
                onClick={cleanupInactiveDevices}
                className="btn-warning-sm flex items-center gap-2"
                disabled={isCleaningInactive}
                title="تنظيف ذكي: حذف الأجهزة المعلقة القديمة (أكثر من يومين) وتنظيف سجلات الوصول القديمة مع الحفاظ على الأجهزة المعتمدة"
              >
                <FaSync className={isCleaningInactive ? 'animate-spin' : ''} />
                <span>{isCleaningInactive ? 'جاري التنظيف...' : 'تنظيف ذكي'}</span>
              </button>

              <button
                onClick={() => setShowCleanupAllConfirm(true)}
                className="btn-danger-sm flex items-center gap-2"
                disabled={isCleaningAll}
                title="حذف جميع الأجهزة مع الحفاظ على الخادم الرئيسي"
              >
                <FaTrash className={isCleaningAll ? 'animate-pulse' : ''} />
                <span>{isCleaningAll ? 'جاري الحذف...' : 'حذف الكل'}</span>
              </button>

              {error && (
                <div className="text-xs text-orange-600 dark:text-orange-400 max-w-xs truncate" title={error}>
                  {error}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* إحصائيات الأجهزة المتصلة - بنفس تصميم مراقبة النظام */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4 mb-8">
          {/* إجمالي الأجهزة */}
          <div className="touch-card stats-card hover:shadow-lg transition-all duration-200">
            <div className="flex justify-between items-start mb-4">
              <div>
                <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">إجمالي الأجهزة</p>
                <p className="text-3xl font-bold text-secondary-900 dark:text-secondary-100">{summary.total_devices}</p>
              </div>
              <div className="p-4 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                <FaNetworkWired className="text-xl" />
              </div>
            </div>
            <div className="flex items-center text-sm text-secondary-500 dark:text-secondary-400">
              <FaCircle className="ml-1 text-xs" />
              جميع الأجهزة
            </div>
          </div>

          {/* الأجهزة المتصلة */}
          <div className="touch-card stats-card hover:shadow-lg transition-all duration-200">
            <div className="flex justify-between items-start mb-4">
              <div>
                <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">متصل الآن</p>
                <p className="text-3xl font-bold text-green-600 dark:text-green-400">{summary.online_devices}</p>
              </div>
              <div className="p-4 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400">
                <FaWifi className="text-xl animate-pulse" />
              </div>
            </div>
            <div className="flex items-center text-sm text-green-600 dark:text-green-400">
              <FaCheckCircle className="ml-1 text-xs" />
              نشط ومتاح
            </div>
          </div>

          {/* الأجهزة المحلية */}
          <div className="touch-card stats-card hover:shadow-lg transition-all duration-200">
            <div className="flex justify-between items-start mb-4">
              <div>
                <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">أجهزة محلية</p>
                <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">{summary.local_devices_count + summary.main_server_count}</p>
              </div>
              <div className="p-4 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400">
                <FaServer className="text-xl" />
              </div>
            </div>
            <div className="flex items-center text-sm text-purple-600 dark:text-purple-400">
              <FaServer className="ml-1 text-xs" />
              شبكة محلية
            </div>
          </div>

          {/* الأجهزة البعيدة */}
          <div className="touch-card stats-card hover:shadow-lg transition-all duration-200">
            <div className="flex justify-between items-start mb-4">
              <div>
                <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">أجهزة بعيدة</p>
                <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">{summary.remote_devices_count}</p>
              </div>
              <div className="p-4 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400">
                <FaDesktop className="text-xl" />
              </div>
            </div>
            <div className="flex items-center text-sm text-orange-600 dark:text-orange-400">
              <FaNetworkWired className="ml-1 text-xs" />
              شبكة محلية وخارجية
            </div>
          </div>

          {/* الأجهزة المعتمدة */}
          <div className="touch-card stats-card hover:shadow-lg transition-all duration-200">
            <div className="flex justify-between items-start mb-4">
              <div>
                <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">أجهزة معتمدة</p>
                <p className="text-3xl font-bold text-emerald-600 dark:text-emerald-400">{summary.approved_devices_count || 0}</p>
              </div>
              <div className="p-4 rounded-full bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400">
                <FaCheckCircle className="text-xl" />
              </div>
            </div>
            <div className="flex items-center text-sm text-emerald-600 dark:text-emerald-400">
              <FaCheckCircle className="ml-1 text-xs" />
              موثوق ومؤمن
            </div>
          </div>

          {/* الأجهزة في الانتظار */}
          <div className="touch-card stats-card hover:shadow-lg transition-all duration-200">
            <div className="flex justify-between items-start mb-4">
              <div>
                <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">في الانتظار</p>
                <p className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{summary.pending_devices_count || 0}</p>
              </div>
              <div className="p-4 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400">
                <FaExclamationTriangle className="text-xl" />
              </div>
            </div>
            <div className="flex items-center text-sm text-yellow-600 dark:text-yellow-400">
              <FaExclamationTriangle className="ml-1 text-xs" />
              يحتاج موافقة
            </div>
          </div>

          {/* الأجهزة المحظورة */}
          <div className="touch-card stats-card hover:shadow-lg transition-all duration-200">
            <div className="flex justify-between items-start mb-4">
              <div>
                <p className="text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1">محظور</p>
                <p className="text-3xl font-bold text-red-600 dark:text-red-400">{summary.blocked_devices_count || 0}</p>
              </div>
              <div className="p-4 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400">
                <FaBan className="text-xl" />
              </div>
            </div>
            <div className="flex items-center text-sm text-red-600 dark:text-red-400">
              <FaExclamationTriangle className="ml-1 text-xs" />
              مرفوض الوصول
            </div>
          </div>


        </div>
      </div>

      {/* عرض الأجهزة إذا لم يكن في وضع الملخص فقط */}
      {!showSummaryOnly && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-lg">
                <FaDesktop className="text-lg text-gray-600 dark:text-gray-400" />
              </div>
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  قائمة الأجهزة
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  عرض تفصيلي لجميع الأجهزة المتصلة
                </p>
              </div>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {devices.length} جهاز
            </div>
          </div>

          {devices.length === 0 ? (
            <div className="text-center py-8">
              <FaNetworkWired className="text-4xl text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">لا توجد أجهزة متصلة حالياً</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6 gap-3">
                {/* ترتيب الأجهزة: الخادم الرئيسي أولاً، ثم باقي الأجهزة */}
                {devices
                  .sort((a, b) => {
                    // الخادم الرئيسي أولاً
                    if (a.is_main_server && !b.is_main_server) return -1;
                    if (!a.is_main_server && b.is_main_server) return 1;
                    // ثم ترتيب حسب آخر وصول
                    return new Date(b.last_access).getTime() - new Date(a.last_access).getTime();
                  })
                  .map((device) => {
                    // استخدام البيانات المحسنة إذا كانت متوفرة
                    const enhancedDevice = getEnhancedDevice(device);
                    return (
                  <div
                    key={device.device_id}
                    className={`bg-white dark:bg-gray-800 rounded-lg p-3 border transition-all duration-200 cursor-pointer relative ${
                      device.approval_status === 'blocked'
                        ? 'border-red-400 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/10 shadow-md opacity-75 hover:opacity-100'
                        : device.is_main_server
                        ? 'border-blue-400 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/10 shadow-md hover:shadow-lg'
                        : device.client_ip === currentClientIp
                        ? 'border-primary-400 bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/10 shadow-md hover:shadow-lg'
                        : 'border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600 shadow-sm hover:shadow-md'
                    }`}
                    onClick={() => {
                      // للأجهزة المحظورة، لا نفتح النافذة
                      if (device.approval_status !== 'blocked') {
                        setSelectedDeviceForDetails(device);
                      }
                    }}
                    title={`${device.hostname} - ${device.is_main_server ? dynamicServerAddress : device.client_ip}
${enhancedDevice.security_level_ar ? `مستوى الأمان: ${enhancedDevice.security_level_ar}` : ''}
${enhancedDevice.access_type_ar ? `نوع الوصول: ${enhancedDevice.access_type_ar}` : ''}
${enhancedDevice.session_duration_formatted ? `مدة الجلسة: ${enhancedDevice.session_duration_formatted}` : ''}
${enhancedDevice.last_activity_formatted ? `آخر نشاط: ${enhancedDevice.last_activity_formatted}` : ''}
${enhancedDevice.dataSource === 'integrated' ? '🔄 بيانات حية محدثة' : ''}`}
                  >
                    {/* شارات الحالة المضغوطة */}
                    <div className="absolute -top-1 -right-1 flex gap-1 z-10">
                      {device.client_ip === currentClientIp && (
                        <div className="bg-primary-600 text-white text-xs px-1.5 py-0.5 rounded-full font-medium shadow-sm">
                          أنت
                        </div>
                      )}
                      {device.is_main_server && (
                        <div className="bg-blue-600 text-white text-xs px-1.5 py-0.5 rounded-full font-medium shadow-sm">
                          خادم
                        </div>
                      )}
                      {device.approval_status === 'approved' && !device.is_main_server && (
                        <div className="bg-green-600 text-white text-xs px-1.5 py-0.5 rounded-full font-medium shadow-sm">
                          معتمد
                        </div>
                      )}
                      {device.approval_status === 'blocked' && (
                        <div className="bg-red-600 text-white text-xs px-1.5 py-0.5 rounded-full font-medium shadow-sm">
                          محظور
                        </div>
                      )}
                      {device.is_advanced_fingerprint && (
                        <div className={`text-white text-xs px-1.5 py-0.5 rounded-full font-medium shadow-sm flex items-center gap-1 ${
                          enhancedDevice.security_level === 'high' ? 'bg-green-600' :
                          enhancedDevice.security_level === 'medium' ? 'bg-yellow-600' :
                          'bg-purple-600'
                        }`}>
                          <FaInfoCircle className="text-xs" />
                          {enhancedDevice.security_level_ar || 'متقدم'}
                        </div>
                      )}
                    </div>

                    {/* محتوى البطاقة المضغوط */}
                    <div className="space-y-2">
                      {/* رأس البطاقة */}
                      <div className="flex items-center gap-2">
                        <div className="relative">
                          <div className={`text-2xl ${
                            device.is_main_server
                              ? 'text-blue-600 dark:text-blue-400'
                              : device.client_ip === currentClientIp
                              ? 'text-primary-600 dark:text-primary-400'
                              : 'text-gray-700 dark:text-gray-300'
                          }`}>
                            {device.is_main_server ? <FaServer /> : getDeviceIcon(device)}
                          </div>
                          {/* مؤشر حالة الاتصال المصغر */}
                          <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border border-white dark:border-gray-800 ${
                            enhancedDevice.status === 'blocked' ? 'bg-red-500' :
                            enhancedDevice.status === 'online' || enhancedDevice.status === 'approved_online' ? 'bg-green-500 animate-pulse' :
                            enhancedDevice.status === 'recently_active' ? 'bg-yellow-500' :
                            enhancedDevice.status === 'pending_approval' ? 'bg-orange-500' : 'bg-gray-400'
                          }`}></div>

                          {/* مؤشر التحديث */}
                          {updatingDevices.has(device.device_id) && (
                            <div className="absolute -top-1 -left-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-sm text-gray-900 dark:text-gray-100 truncate">
                            {device.is_main_server ? dynamicServerAddress : device.client_ip}
                          </h3>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            {device.hostname}
                          </p>
                        </div>
                      </div>

                      {/* معلومات المستخدم المضغوطة مع البيانات الحية */}
                      <div className={`rounded px-2 py-1 text-center ${
                        enhancedDevice.current_user
                          ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                          : 'bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                      }`}>
                        <div className="flex items-center justify-center gap-1">
                          <User className="w-3 h-3" />
                          <span className="text-xs font-medium truncate">
                            {enhancedDevice.current_user || 'لا يوجد مستخدم'}
                          </span>
                          {/* مؤشر البيانات الحية */}
                          {enhancedDevice.dataSource === 'integrated' && (
                            <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse" title="بيانات حية"></div>
                          )}
                        </div>
                        {/* معلومات إضافية للبيانات الحية */}
                        {enhancedDevice.last_activity_formatted && (
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                            {enhancedDevice.last_activity_formatted}
                          </div>
                        )}
                      </div>

                      {/* حالة الجهاز المضغوطة */}
                      <div className="flex gap-1">
                        {/* حالة الموافقة للأجهزة البعيدة */}
                        {!device.is_main_server && (
                          <div className={`flex-1 flex items-center justify-center gap-1 rounded px-1.5 py-1 text-xs ${
                            device.approval_status === 'approved'
                              ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                              : device.approval_status === 'pending' || device.status === 'pending_approval'
                              ? 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300'
                              : 'bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                          }`}>
                            {device.approval_status === 'approved' ? (
                              <>
                                <CheckCircle className="w-3 h-3" />
                                <span className="font-medium hidden sm:inline">معتمد</span>
                              </>
                            ) : device.approval_status === 'pending' || device.status === 'pending_approval' ? (
                              <>
                                <Clock className="w-3 h-3" />
                                <span className="font-medium hidden sm:inline">انتظار</span>
                              </>
                            ) : (
                              <>
                                <XCircle className="w-3 h-3" />
                                <span className="font-medium hidden sm:inline">غير محدد</span>
                              </>
                            )}
                          </div>
                        )}

                        {/* حالة النشاط المحسنة */}
                        <div className={`flex-1 flex items-center justify-center gap-1 rounded px-1.5 py-1 text-xs ${
                          enhancedDevice.status === 'online' || enhancedDevice.status === 'approved_online'
                            ? 'bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300'
                            : enhancedDevice.status === 'recently_active'
                            ? 'bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300'
                            : 'bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                        }`}>
                          {enhancedDevice.status === 'online' || enhancedDevice.status === 'approved_online' ? (
                            <>
                              <Zap className="w-3 h-3 animate-pulse" />
                              <span className="font-medium hidden sm:inline">
                                {enhancedDevice.status_ar || 'متصل'}
                              </span>
                            </>
                          ) : enhancedDevice.status === 'recently_active' ? (
                            <>
                              <Clock className="w-3 h-3" />
                              <span className="font-medium hidden sm:inline">
                                {enhancedDevice.status_ar || 'نشط مؤخراً'}
                              </span>
                            </>
                          ) : (
                            <>
                              <XCircle className="w-3 h-3" />
                              <span className="font-medium hidden sm:inline">
                                {enhancedDevice.status_ar || 'غير متصل'}
                              </span>
                            </>
                          )}
                          {/* مؤشر مدة الجلسة للأجهزة النشطة */}
                          {enhancedDevice.session_duration_minutes && enhancedDevice.session_duration_minutes > 0 && (
                            <div className="text-xs opacity-75 ml-1" title={`مدة الجلسة: ${enhancedDevice.session_duration_formatted || enhancedDevice.session_duration_minutes + ' دقيقة'}`}>
                              {enhancedDevice.session_duration_minutes < 60 ?
                                `${enhancedDevice.session_duration_minutes}د` :
                                `${Math.floor(enhancedDevice.session_duration_minutes / 60)}س`}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* ✅ Overlay زر إلغاء الحظر للأجهزة المحظورة */}
                    {device.approval_status === 'blocked' && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            unblockDevice(device.device_id);
                          }}
                          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
                        >
                          <FaCheckCircle />
                          إلغاء الحظر
                        </button>
                      </div>
                    )}

                  </div>
                    );
                  })}
            </div>
          )}
        </div>
      )}



      {/* نافذة تأكيد حظر الجهاز */}
      {createPortal(
        <ConfirmModal
          isOpen={showBlockConfirm}
          onClose={() => {
            setShowBlockConfirm(false);
            setDeviceToBlock(null);
          }}
          onConfirm={blockDevice}
          title="تأكيد حظر الجهاز"
          message={`هل أنت متأكد من حظر الجهاز "${deviceToBlock?.hostname}" (${deviceToBlock?.client_ip})؟`}
          description="سيتم منع هذا الجهاز من الوصول إلى النظام نهائياً. يمكنك إلغاء الحظر لاحقاً من إعدادات أمان الأجهزة."
          confirmText="حظر الجهاز"
          cancelText="إلغاء"
          type="danger"
          isLoading={isBlocking}
        />,
        document.body
      )}



      {/* نافذة تأكيد حذف جميع الأجهزة */}
      {createPortal(
        <DeleteAllDevicesModal
          isOpen={showCleanupAllConfirm}
          onClose={() => setShowCleanupAllConfirm(false)}
          onConfirm={cleanupAllDevices}
          isLoading={isCleaningAll}
        />,
        document.body
      )}

      {/* نافذة التفاصيل المتقدمة */}
      <DeviceDetailsModal
        isOpen={!!selectedDeviceForDetails}
        onClose={() => setSelectedDeviceForDetails(null)}
        device={selectedDeviceForDetails}
        onBlock={openBlockConfirm}
      />
    </div>
  );
};

export default ConnectedDevices;
