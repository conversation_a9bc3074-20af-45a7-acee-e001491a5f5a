import React, { useState } from 'react';
import { FaTrash, FaTimes, FaCheck } from 'react-icons/fa';
import { Trash2, AlertTriangle, X } from './ui/icons';
import Modal from './Modal';
import { ConnectedDevice } from '../hooks/useConnectedDevices';

interface DeleteDeviceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  device: ConnectedDevice | null;
  isLoading?: boolean;
}

const DeleteDeviceModal: React.FC<DeleteDeviceModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  device,
  isLoading = false
}) => {
  const [confirmText, setConfirmText] = useState('');
  const isConfirmValid = confirmText === 'DELETE';

  const handleConfirm = () => {
    if (isConfirmValid) {
      onConfirm();
    }
  };

  const handleClose = () => {
    setConfirmText('');
    onClose();
  };

  if (!device) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="حذف الجهاز نهائياً"
      size="md"
    >
      <div className="space-y-6">
        {/* رمز التحذير */}
        <div className="text-center">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30 mb-4">
            <Trash2 className="h-8 w-8 text-red-600 dark:text-red-400" />
          </div>
        </div>

        {/* معلومات الجهاز */}
        <div className="text-center space-y-3">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            تحذير: حذف نهائي للجهاز
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-300">
            هل أنت متأكد من حذف هذا الجهاز نهائياً من النظام؟
          </p>
        </div>

        {/* بيانات الجهاز */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-500 dark:text-gray-400">اسم الجهاز:</span>
            <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">{device.hostname}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-500 dark:text-gray-400">عنوان IP:</span>
            <span className="text-sm font-mono text-gray-900 dark:text-gray-100">{device.client_ip}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-500 dark:text-gray-400">نوع الجهاز:</span>
            <span className="text-sm text-gray-900 dark:text-gray-100">{device.device_type}</span>
          </div>
        </div>

        {/* تفاصيل العملية */}
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-red-800 dark:text-red-200">
                سيتم حذف البيانات التالية نهائياً:
              </h4>
              <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                <li>• جميع بيانات الجهاز من قاعدة البيانات</li>
                <li>• بصمة الجهاز وسجلات التاريخ</li>
                <li>• سجلات الوصول والأنشطة</li>
                <li>• معلومات الموافقة والحظر</li>
              </ul>
            </div>
          </div>
        </div>

        {/* تحذير إضافي */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
          <p className="text-sm text-yellow-800 dark:text-yellow-200 text-center">
            ⚠️ سيصبح الجهاز وكأنه لم يتصل بالتطبيق أبداً
          </p>
        </div>

        {/* حقل التأكيد */}
        <div className="space-y-3">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            لتأكيد العملية، اكتب كلمة <span className="font-bold text-red-600 dark:text-red-400">"DELETE"</span> بالأحرف الكبيرة:
          </label>
          <input
            type="text"
            value={confirmText}
            onChange={(e) => setConfirmText(e.target.value)}
            placeholder="اكتب DELETE للتأكيد"
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
                     bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                     focus:ring-2 focus:ring-red-500 focus:border-red-500
                     text-center font-mono text-lg tracking-wider
                     placeholder:text-gray-400 dark:placeholder:text-gray-500"
            autoFocus
            dir="ltr"
            disabled={isLoading}
          />
          
          {/* رسائل التحقق */}
          {confirmText && confirmText !== 'DELETE' && (
            <p className="text-sm text-red-600 dark:text-red-400 text-center">
              يجب كتابة "DELETE" بالضبط بالأحرف الكبيرة
            </p>
          )}
          {confirmText === 'DELETE' && (
            <p className="text-sm text-green-600 dark:text-green-400 text-center flex items-center justify-center gap-1">
              <FaCheck className="h-4 w-4" />
              تم التأكيد بنجاح - جاهز للحذف
            </p>
          )}
        </div>

        {/* أزرار الإجراءات */}
        <div className="flex gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="flex-1 btn-secondary flex items-center justify-center gap-2"
          >
            <X className="h-4 w-4" />
            <span>إلغاء</span>
          </button>

          <button
            onClick={handleConfirm}
            disabled={!isConfirmValid || isLoading}
            className={`flex-1 btn-danger flex items-center justify-center gap-2 ${
              !isConfirmValid || isLoading
                ? 'opacity-50 cursor-not-allowed'
                : ''
            }`}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>جاري الحذف...</span>
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                <span>حذف نهائي</span>
              </>
            )}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteDeviceModal;
